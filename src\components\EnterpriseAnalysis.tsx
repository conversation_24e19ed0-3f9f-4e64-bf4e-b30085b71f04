import React, { useState } from 'react';
import { Building2, Citrus as Industry, Calendar, TrendingUp, Star, Globe, MapPin, Users, DollarSign } from 'lucide-react';

const EnterpriseAnalysis = () => {
  const [selectedEnterprise, setSelectedEnterprise] = useState(null);

  const enterprises = [
    {
      id: 1,
      name: '腾讯科技',
      industry: '互联网',
      size: '大型企业',
      stage: '成熟期',
      location: '深圳',
      revenue: '5000亿+',
      employees: '10万+',
      founded: '1998',
      status: '行业领导者',
      score: 95
    },
    {
      id: 2,
      name: '阿里巴巴',
      industry: '电子商务',
      size: '大型企业',
      stage: '成熟期',
      location: '杭州',
      revenue: '8000亿+',
      employees: '25万+',
      founded: '1999',
      status: '行业领导者',
      score: 98
    },
    {
      id: 3,
      name: '字节跳动',
      industry: '移动互联网',
      size: '大型企业',
      stage: '成长期',
      location: '北京',
      revenue: '3000亿+',
      employees: '15万+',
      founded: '2012',
      status: '快速成长',
      score: 92
    },
    {
      id: 4,
      name: '美团',
      industry: '本地生活',
      size: '中大型企业',
      stage: '成熟期',
      location: '北京',
      revenue: '1800亿+',
      employees: '8万+',
      founded: '2010',
      status: '行业领先',
      score: 88
    }
  ];

  const analysisCategories = [
    {
      title: '企业性质',
      icon: Building2,
      color: 'blue',
      data: [
        { label: '民营企业', value: 65, color: 'bg-blue-500' },
        { label: '国有企业', value: 25, color: 'bg-green-500' },
        { label: '外资企业', value: 10, color: 'bg-purple-500' }
      ]
    },
    {
      title: '行业分布',
      icon: Industry,
      color: 'green',
      data: [
        { label: '互联网', value: 35, color: 'bg-blue-500' },
        { label: '制造业', value: 25, color: 'bg-green-500' },
        { label: '金融服务', value: 20, color: 'bg-purple-500' },
        { label: '其他', value: 20, color: 'bg-orange-500' }
      ]
    },
    {
      title: '发展阶段',
      icon: TrendingUp,
      color: 'purple',
      data: [
        { label: '成熟期', value: 45, color: 'bg-blue-500' },
        { label: '成长期', value: 35, color: 'bg-green-500' },
        { label: '初创期', value: 20, color: 'bg-purple-500' }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">企业信息分析</h1>
          <p className="text-slate-400">深度分析目标企业的基本信息和发展状况</p>
        </div>
        <button className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200">
          新增企业
        </button>
      </div>

      {/* Analysis Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {analysisCategories.map((category) => {
          const Icon = category.icon;
          return (
            <div key={category.title} className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
              <div className="flex items-center space-x-3 mb-4">
                <Icon className="w-6 h-6 text-blue-400" />
                <h3 className="text-lg font-semibold text-white">{category.title}</h3>
              </div>
              <div className="space-y-3">
                {category.data.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                      <span className="text-slate-300 text-sm">{item.label}</span>
                    </div>
                    <span className="text-white font-medium">{item.value}%</span>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Enterprise List */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50">
        <div className="p-6 border-b border-slate-700/50">
          <h2 className="text-xl font-semibold text-white">目标企业列表</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {enterprises.map((enterprise) => (
              <div
                key={enterprise.id}
                className="bg-slate-700/50 rounded-lg p-5 border border-slate-600/50 hover:border-blue-500/50 transition-colors duration-200 cursor-pointer"
                onClick={() => setSelectedEnterprise(enterprise)}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">{enterprise.name}</h3>
                  <div className="flex items-center space-x-2">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-slate-300">{enterprise.score}</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <Industry className="w-4 h-4 text-blue-400" />
                    <span className="text-sm text-slate-300">{enterprise.industry}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-green-400" />
                    <span className="text-sm text-slate-300">{enterprise.employees}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin className="w-4 h-4 text-purple-400" />
                    <span className="text-sm text-slate-300">{enterprise.location}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-4 h-4 text-orange-400" />
                    <span className="text-sm text-slate-300">{enterprise.revenue}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    enterprise.stage === '成熟期' ? 'bg-blue-500/20 text-blue-300' :
                    enterprise.stage === '成长期' ? 'bg-green-500/20 text-green-300' :
                    'bg-purple-500/20 text-purple-300'
                  }`}>
                    {enterprise.stage}
                  </span>
                  <span className="text-xs text-slate-400">成立于 {enterprise.founded}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Selected Enterprise Details */}
      {selectedEnterprise && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <h2 className="text-xl font-semibold text-white mb-4">企业详细信息</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-white mb-3">基本信息</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-400">企业名称</span>
                  <span className="text-white">{selectedEnterprise.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">所属行业</span>
                  <span className="text-white">{selectedEnterprise.industry}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">企业规模</span>
                  <span className="text-white">{selectedEnterprise.size}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">发展阶段</span>
                  <span className="text-white">{selectedEnterprise.stage}</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-medium text-white mb-3">经营状况</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-400">营业收入</span>
                  <span className="text-white">{selectedEnterprise.revenue}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">员工人数</span>
                  <span className="text-white">{selectedEnterprise.employees}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">成立时间</span>
                  <span className="text-white">{selectedEnterprise.founded}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">行业地位</span>
                  <span className="text-white">{selectedEnterprise.status}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnterpriseAnalysis;