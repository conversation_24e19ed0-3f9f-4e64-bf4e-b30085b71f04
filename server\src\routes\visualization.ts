import { Router } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler.js';
import { enterpriseService } from '../services/enterpriseService.js';
import { neo4jService } from '../services/neo4jService.js';
import { postgresService } from '../services/postgresService.js';
import { apiLogger } from '../utils/logger.js';
import Joi from 'joi';

const router = Router();

// Validation schemas
const networkQuerySchema = Joi.object({
  industry: Joi.string().optional(),
  limit: Joi.number().integer().min(1).max(100).default(50)
});

const trendsQuerySchema = Joi.object({
  period: Joi.string().valid('week', 'month', 'quarter', 'year').default('month'),
  metric: Joi.string().valid('enterprises', 'analyses', 'score').default('enterprises')
});

/**
 * @route GET /api/visualization/network-data
 * @desc Get network graph data for enterprises
 * @access Public
 */
router.get('/network-data', asyncHandler(async (req, res) => {
  apiLogger.info('Network data request received', { query: req.query });

  // Validate query parameters
  const { error, value } = networkQuerySchema.validate(req.query);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  const { industry, limit } = value;

  try {
    // Get enterprises
    const enterpriseQuery: any = { limit };
    if (industry) {
      enterpriseQuery.industry = industry;
    }

    const enterpriseResult = await enterpriseService.getEnterprises(enterpriseQuery);
    const enterprises = enterpriseResult.enterprises;

    // Build network data
    const nodes: any[] = [];
    const links: any[] = [];

    // Add enterprise nodes
    enterprises.forEach(enterprise => {
      nodes.push({
        id: enterprise._id.toString(),
        name: enterprise.name,
        type: 'enterprise',
        industry: enterprise.industry,
        location: enterprise.location,
        score: enterprise.score || 75,
        size: Math.max(15, (enterprise.score || 75) / 5),
        employees: enterprise.employees,
        stage: enterprise.stage
      });
    });

    // Add industry nodes
    const industries = [...new Set(enterprises.map(e => e.industry))];
    industries.forEach(industry => {
      const industryEnterprises = enterprises.filter(e => e.industry === industry);
      nodes.push({
        id: `industry_${industry}`,
        name: industry,
        type: 'industry',
        size: Math.max(20, industryEnterprises.length * 3),
        count: industryEnterprises.length
      });
    });

    // Add location nodes
    const locations = [...new Set(enterprises.map(e => e.location).filter(Boolean))];
    locations.forEach(location => {
      const locationEnterprises = enterprises.filter(e => e.location === location);
      nodes.push({
        id: `location_${location}`,
        name: location,
        type: 'location',
        size: Math.max(15, locationEnterprises.length * 2),
        count: locationEnterprises.length
      });
    });

    // Add enterprise to industry links
    enterprises.forEach(enterprise => {
      links.push({
        source: enterprise._id.toString(),
        target: `industry_${enterprise.industry}`,
        type: 'BELONGS_TO',
        strength: 0.8
      });

      if (enterprise.location) {
        links.push({
          source: enterprise._id.toString(),
          target: `location_${enterprise.location}`,
          type: 'LOCATED_IN',
          strength: 0.6
        });
      }
    });

    // Add competitive relationships (same industry, similar size)
    for (let i = 0; i < enterprises.length - 1; i++) {
      for (let j = i + 1; j < enterprises.length; j++) {
        const enterprise1 = enterprises[i];
        const enterprise2 = enterprises[j];

        if (enterprise1.industry === enterprise2.industry) {
          const sizeDiff = Math.abs((enterprise1.employees || 0) - (enterprise2.employees || 0));
          if (sizeDiff < 1000) { // Similar size companies
            links.push({
              source: enterprise1._id.toString(),
              target: enterprise2._id.toString(),
              type: 'COMPETES_WITH',
              strength: 0.3
            });
          }
        }
      }
    }

    res.json({
      success: true,
      data: {
        nodes,
        links,
        stats: {
          totalNodes: nodes.length,
          totalLinks: links.length,
          enterprises: enterprises.length,
          industries: industries.length,
          locations: locations.length
        }
      }
    });

  } catch (error) {
    apiLogger.error('Failed to get network data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get network data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * @route GET /api/visualization/trends
 * @desc Get trend data for visualization
 * @access Public
 */
router.get('/trends', asyncHandler(async (req, res) => {
  apiLogger.info('Trends data request received', { query: req.query });

  // Validate query parameters
  const { error, value } = trendsQuerySchema.validate(req.query);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  const { period, metric } = value;

  try {
    // Generate mock trend data based on period
    const now = new Date();
    const data: any[] = [];
    let periods: string[] = [];

    switch (period) {
      case 'week':
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          periods.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
        }
        break;
      case 'month':
        for (let i = 5; i >= 0; i--) {
          const date = new Date(now);
          date.setMonth(date.getMonth() - i);
          periods.push(date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'short' }));
        }
        break;
      case 'quarter':
        for (let i = 3; i >= 0; i--) {
          const date = new Date(now);
          date.setMonth(date.getMonth() - i * 3);
          periods.push(`Q${Math.floor(date.getMonth() / 3) + 1} ${date.getFullYear()}`);
        }
        break;
      case 'year':
        for (let i = 4; i >= 0; i--) {
          const date = new Date(now);
          date.setFullYear(date.getFullYear() - i);
          periods.push(date.getFullYear().toString());
        }
        break;
    }

    // Generate data based on metric
    periods.forEach((period, index) => {
      let value: number;
      switch (metric) {
        case 'enterprises':
          value = 20 + index * 5 + Math.floor(Math.random() * 10);
          break;
        case 'analyses':
          value = 15 + index * 3 + Math.floor(Math.random() * 8);
          break;
        case 'score':
          value = 75 + Math.random() * 20;
          break;
        default:
          value = 0;
      }

      data.push({
        period,
        value: Math.round(value * 100) / 100,
        metric
      });
    });

    res.json({
      success: true,
      data: {
        trends: data,
        period,
        metric,
        summary: {
          total: data.reduce((sum, item) => sum + item.value, 0),
          average: data.reduce((sum, item) => sum + item.value, 0) / data.length,
          growth: data.length > 1 ? 
            ((data[data.length - 1].value - data[0].value) / data[0].value * 100) : 0
        }
      }
    });

  } catch (error) {
    apiLogger.error('Failed to get trends data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trends data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

/**
 * @route GET /api/visualization/analytics-summary
 * @desc Get analytics summary for dashboard
 * @access Public
 */
router.get('/analytics-summary', asyncHandler(async (req, res) => {
  apiLogger.info('Analytics summary request received');

  try {
    // Get enterprise stats
    const enterpriseStats = await enterpriseService.getEnterpriseStats();
    
    // Get analytics stats from PostgreSQL
    const analyticsStats = await postgresService.getAnalyticsStats();

    // Get recent user activities
    const { activities } = await postgresService.getUserActivities(undefined, 10);

    const summary = {
      enterprises: {
        total: enterpriseStats.total,
        byIndustry: enterpriseStats.byIndustry,
        byStage: enterpriseStats.byStage,
        byLocation: enterpriseStats.byLocation,
        averageScore: enterpriseStats.averageScore
      },
      analytics: {
        totalAnalyses: analyticsStats.totalAnalyses,
        analysesByType: analyticsStats.analysesByType,
        recentAnalyses: analyticsStats.recentAnalyses,
        averageConfidence: analyticsStats.averageConfidence
      },
      activities: activities.slice(0, 5).map(activity => ({
        action: activity.action,
        resourceType: activity.resource_type,
        resourceId: activity.resource_id,
        createdAt: activity.created_at,
        metadata: activity.metadata
      }))
    };

    res.json({
      success: true,
      data: summary
    });

  } catch (error) {
    apiLogger.error('Failed to get analytics summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get analytics summary',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

export { router as visualizationRoutes };
