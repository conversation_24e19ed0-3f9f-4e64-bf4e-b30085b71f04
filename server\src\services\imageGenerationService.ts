import * as tencentcloud from 'tencentcloud-sdk-nodejs';
import { aiLogger } from '../utils/logger.js';
import { CustomError } from '../middleware/errorHandler.js';

const AiartClient = tencentcloud.aiart.v20221229.Client;

export interface ImageGenerationRequest {
  prompt: string;
  style?: 'realistic' | 'cartoon' | 'anime' | 'oil_painting' | 'watercolor';
  resolution?: '512x512' | '768x768' | '1024x1024';
  count?: number;
  seed?: number;
}

export interface ImageGenerationResponse {
  success: boolean;
  images?: Array<{
    url: string;
    base64?: string;
  }>;
  taskId?: string;
  error?: string;
  processingTime?: number;
}

export interface EnterpriseVisualizationRequest {
  enterpriseName: string;
  industry: string;
  stage: string;
  analysisData: any;
  visualizationType: 'logo_concept' | 'office_scene' | 'business_chart' | 'team_portrait';
}

export class ImageGenerationService {
  private client: any;

  constructor() {
    if (!process.env.TENCENT_SECRET_ID || !process.env.TENCENT_SECRET_KEY) {
      aiLogger.warn('Tencent Cloud credentials not configured, image generation will be disabled');
      this.client = null;
    } else {
      const clientConfig = {
        credential: {
          secretId: process.env.TENCENT_SECRET_ID,
          secretKey: process.env.TENCENT_SECRET_KEY,
        },
        region: process.env.TENCENT_REGION || 'ap-beijing',
        profile: {
          httpProfile: {
            endpoint: 'aiart.tencentcloudapi.com',
          },
        },
      };
      this.client = new AiartClient(clientConfig);
    }
  }

  /**
   * 生成企业可视化图像
   */
  async generateEnterpriseVisualization(
    request: EnterpriseVisualizationRequest
  ): Promise<ImageGenerationResponse> {
    if (!this.client) {
      return {
        success: false,
        error: 'Image generation service not configured'
      };
    }

    const startTime = Date.now();

    try {
      const prompt = this.buildVisualizationPrompt(request);
      
      const imageRequest: ImageGenerationRequest = {
        prompt,
        style: this.getStyleForVisualization(request.visualizationType),
        resolution: '1024x1024',
        count: 1
      };

      const result = await this.generateImage(imageRequest);
      
      if (result.success) {
        aiLogger.info('Enterprise visualization generated', {
          enterprise: request.enterpriseName,
          type: request.visualizationType,
          processingTime: Date.now() - startTime
        });
      }

      return result;

    } catch (error) {
      aiLogger.error('Failed to generate enterprise visualization:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 基础图像生成
   */
  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResponse> {
    if (!this.client) {
      return {
        success: false,
        error: 'Image generation service not configured'
      };
    }

    const startTime = Date.now();

    try {
      const params = {
        Prompt: request.prompt,
        Style: this.mapStyleToTencent(request.style || 'realistic'),
        Resolution: request.resolution || '1024x1024',
        Num: request.count || 1,
        Seed: request.seed || Math.floor(Math.random() * 1000000),
        LogoAdd: 0, // 不添加水印
        RspImgType: 'url' // 返回URL
      };

      aiLogger.info('Generating image with Tencent Cloud', { 
        prompt: request.prompt.substring(0, 100),
        style: request.style,
        resolution: request.resolution
      });

      const response = await this.client.TextToImage(params);
      
      if (response.ResultImage && response.ResultImage.length > 0) {
        const images = response.ResultImage.map((img: any) => ({
          url: img
        }));

        const processingTime = Date.now() - startTime;

        aiLogger.info('Image generation completed', {
          imageCount: images.length,
          processingTime
        });

        return {
          success: true,
          images,
          processingTime
        };
      } else {
        throw new Error('No images generated');
      }

    } catch (error) {
      aiLogger.error('Tencent Cloud image generation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Image generation failed'
      };
    }
  }

  /**
   * 构建可视化提示词
   */
  private buildVisualizationPrompt(request: EnterpriseVisualizationRequest): string {
    const { enterpriseName, industry, stage, analysisData, visualizationType } = request;

    const basePrompts = {
      logo_concept: `Professional logo design concept for ${enterpriseName}, a ${stage} ${industry} company. Modern, clean, minimalist design. Corporate identity. High quality, vector-style illustration.`,
      
      office_scene: `Modern office environment of ${enterpriseName}, a ${industry} company in ${stage}. Professional workspace, contemporary design, business atmosphere. Bright lighting, clean architecture.`,
      
      business_chart: `Professional business analytics dashboard and charts representing ${enterpriseName}'s performance. Data visualization, graphs, metrics, modern UI design. Clean, corporate style.`,
      
      team_portrait: `Professional team portrait of ${enterpriseName} employees. ${industry} professionals in modern office setting. Diverse, confident, business attire. Corporate photography style.`
    };

    let prompt = basePrompts[visualizationType] || basePrompts.logo_concept;

    // 根据分析数据增强提示词
    if (analysisData) {
      if (analysisData.overallScore > 85) {
        prompt += ' Premium quality, successful, thriving business atmosphere.';
      }
      if (analysisData.riskLevel === 'low') {
        prompt += ' Stable, reliable, trustworthy appearance.';
      }
      if (stage === '成长期') {
        prompt += ' Dynamic, innovative, growth-oriented design.';
      }
    }

    // 添加质量和风格要求
    prompt += ' High resolution, professional quality, commercial use, 4K detail.';

    return prompt;
  }

  /**
   * 获取可视化类型对应的风格
   */
  private getStyleForVisualization(type: string): 'realistic' | 'cartoon' | 'anime' | 'oil_painting' | 'watercolor' {
    const styleMap = {
      logo_concept: 'realistic' as const,
      office_scene: 'realistic' as const,
      business_chart: 'realistic' as const,
      team_portrait: 'realistic' as const
    };

    return styleMap[type as keyof typeof styleMap] || 'realistic';
  }

  /**
   * 映射风格到腾讯云参数
   */
  private mapStyleToTencent(style: string): string {
    const styleMap = {
      realistic: '201', // 写实风格
      cartoon: '202',   // 卡通风格
      anime: '203',     // 动漫风格
      oil_painting: '204', // 油画风格
      watercolor: '205'    // 水彩风格
    };

    return styleMap[style as keyof typeof styleMap] || '201';
  }

  /**
   * 生成企业分析报告的可视化图表
   */
  async generateAnalysisVisualization(
    enterpriseName: string,
    analysisData: any
  ): Promise<ImageGenerationResponse> {
    const prompt = `
    Professional business analytics infographic for ${enterpriseName}. 
    Data visualization showing:
    - Overall score: ${analysisData.overallScore || 'N/A'}
    - Key performance indicators
    - Growth metrics and trends
    - Risk assessment visualization
    - Modern dashboard design
    - Clean, corporate style
    - High contrast, readable charts
    - Professional color scheme (blue, gray, white)
    - 4K quality, commercial use
    `;

    return this.generateImage({
      prompt: prompt.trim(),
      style: 'realistic',
      resolution: '1024x1024',
      count: 1
    });
  }

  /**
   * 批量生成企业相关图像
   */
  async generateEnterpriseBatch(
    request: EnterpriseVisualizationRequest
  ): Promise<{
    logo: ImageGenerationResponse;
    office: ImageGenerationResponse;
    chart: ImageGenerationResponse;
  }> {
    const [logo, office, chart] = await Promise.all([
      this.generateEnterpriseVisualization({
        ...request,
        visualizationType: 'logo_concept'
      }),
      this.generateEnterpriseVisualization({
        ...request,
        visualizationType: 'office_scene'
      }),
      this.generateEnterpriseVisualization({
        ...request,
        visualizationType: 'business_chart'
      })
    ]);

    return { logo, office, chart };
  }

  /**
   * 检查服务状态
   */
  async checkServiceStatus(): Promise<{
    available: boolean;
    configured: boolean;
    error?: string;
  }> {
    if (!this.client) {
      return {
        available: false,
        configured: false,
        error: 'Tencent Cloud credentials not configured'
      };
    }

    try {
      // 尝试生成一个简单的测试图像
      const testResult = await this.generateImage({
        prompt: 'simple test image, white background, minimal',
        style: 'realistic',
        resolution: '512x512',
        count: 1
      });

      return {
        available: testResult.success,
        configured: true,
        ...(testResult.success ? {} : { error: testResult.error || 'Unknown error' })
      };
    } catch (error) {
      return {
        available: false,
        configured: true,
        error: error instanceof Error ? error.message : 'Service test failed'
      };
    }
  }
}

export const imageGenerationService = new ImageGenerationService();
