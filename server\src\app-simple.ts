import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '🎉 企业画像师平台 - AI原生企业分析系统',
    description: '基于DeepSeek AI的智能企业画像分析平台',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      api: {
        ai: '/api/ai',
        enterprises: '/api/enterprises',
        analysis: '/api/analysis',
        visualization: '/api/visualization'
      }
    },
    features: [
      'AI智能分析',
      '企业画像生成',
      '风险评估',
      '投资价值分析',
      '数据可视化',
      '关系网络图'
    ],
    status: 'operational'
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '企业画像师后端服务正在运行',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    mode: process.env.NODE_ENV || 'development',
    services: {
      ai: 'operational',
      database: 'ready',
      status: 'healthy'
    },
    project: 'enterprise-profiler'
  });
});

// Simple API test endpoints
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API测试成功',
    timestamp: new Date().toISOString()
  });
});

// Mock enterprises endpoint
app.get('/api/enterprises', (req, res) => {
  res.json({
    success: true,
    data: {
      enterprises: [
        {
          _id: '1',
          name: '腾讯科技',
          industry: '互联网',
          stage: '成熟期',
          score: 95
        },
        {
          _id: '2', 
          name: '阿里巴巴',
          industry: '电子商务',
          stage: '成熟期',
          score: 98
        }
      ],
      pagination: {
        total: 2,
        page: 1,
        limit: 20
      }
    }
  });
});

// AI status endpoint
app.get('/api/ai/status', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'operational',
      services: {
        deepseek: {
          available: !!process.env.DEEPSEEK_API_KEY,
          model: 'deepseek-reasoner'
        },
        backup: {
          available: !!process.env.BACKUP_AI_API_KEY,
          model: 'gpt-3.5-turbo'
        }
      }
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `路径 ${req.originalUrl} 未找到`
    },
    availableEndpoints: {
      root: '/',
      health: '/health',
      test: '/api/test',
      enterprises: '/api/enterprises',
      aiStatus: '/api/ai/status'
    },
    tip: '访问根路径 / 查看完整API信息'
  });
});

// Error handler
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', err);
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: '服务器内部错误'
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Enterprise Profiler Server (Simple) running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 CORS enabled for: ${process.env.CORS_ORIGIN || 'http://localhost:5173'}`);
  console.log(`🌐 Visit: http://localhost:${PORT} for API info`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

export default app;
