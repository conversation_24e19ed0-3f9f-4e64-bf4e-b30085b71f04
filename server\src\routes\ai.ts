import { Router, type Request, type Response } from 'express';
import { as<PERSON><PERSON>and<PERSON> } from '../middleware/errorHandler.js';
import { getAIService } from '../services/aiService.js';
import { intelligentAnalysisService } from '../services/intelligentAnalysisService.js';
import { imageGenerationService } from '../services/imageGenerationService.js';
import { apiLogger } from '../utils/logger.js';
import Joi from 'joi';

const router = Router();

// Validation schemas
const enterpriseAnalysisSchema = Joi.object({
  companyName: Joi.string().required().min(1).max(200),
  industry: Joi.string().optional().max(100),
  description: Joi.string().optional().max(1000),
  targetConditions: Joi.object({
    industry: Joi.string().optional(),
    size: Joi.string().optional(),
    stage: Joi.string().optional(),
    location: Joi.string().optional()
  }).optional()
});

const matchingSchema = Joi.object({
  enterprises: Joi.array().items(Joi.object()).required(),
  targetConditions: Joi.object().required()
});

const pathwaySchema = Joi.object({
  enterpriseData: Joi.object().required(),
  roleData: Joi.object().required()
});

const intelligentAnalysisSchema = Joi.object({
  enterpriseId: Joi.string().required(),
  analysisTypes: Joi.array().items(
    Joi.string().valid('profile', 'risk', 'investment', 'competitive', 'pathway')
  ).required(),
  includeCompetitors: Joi.boolean().default(false),
  depth: Joi.string().valid('basic', 'standard', 'comprehensive').default('standard')
});

const imageGenerationSchema = Joi.object({
  enterpriseName: Joi.string().required(),
  industry: Joi.string().required(),
  stage: Joi.string().required(),
  analysisData: Joi.object().default({}),
  visualizationType: Joi.string().valid('logo_concept', 'office_scene', 'business_chart', 'team_portrait').required()
});

/**
 * @route POST /api/ai/analyze-enterprise
 * @desc Analyze enterprise profile using AI
 * @access Public (will add auth later)
 */
router.post('/analyze-enterprise', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Enterprise analysis request received', { body: req.body });

  // Validate request
  const { error, value } = enterpriseAnalysisSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Call AI service
  const aiService = getAIService();
  const result = await aiService.analyzeEnterprise(value);

  if (!result.success) {
    return res.status(500).json({
      success: false,
      message: 'AI analysis failed',
      error: result.error
    });
  }

  res.json({
    success: true,
    data: {
      analysis: result.data,
      usage: result.usage,
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * @route POST /api/ai/generate-matching
 * @desc Generate intelligent matching recommendations
 * @access Public
 */
router.post('/generate-matching', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Matching generation request received');

  // Validate request
  const { error, value } = matchingSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Call AI service
  const aiService = getAIService();
  const result = await aiService.generateMatchingRecommendations(
    value.enterprises,
    value.targetConditions
  );

  if (!result.success) {
    return res.status(500).json({
      success: false,
      message: 'Matching generation failed',
      error: result.error
    });
  }

  res.json({
    success: true,
    data: {
      recommendations: result.data,
      usage: result.usage,
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * @route POST /api/ai/analyze-pathway
 * @desc Analyze user decision pathway
 * @access Public
 */
router.post('/analyze-pathway', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Pathway analysis request received');

  // Validate request
  const { error, value } = pathwaySchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Call AI service
  const aiService = getAIService();
  const result = await aiService.analyzeUserPathway(
    value.enterpriseData,
    value.roleData
  );

  if (!result.success) {
    return res.status(500).json({
      success: false,
      message: 'Pathway analysis failed',
      error: result.error
    });
  }

  res.json({
    success: true,
    data: {
      pathway: result.data,
      usage: result.usage,
      timestamp: new Date().toISOString()
    }
  });
}));

/**
 * @route POST /api/ai/intelligent-analysis
 * @desc Perform comprehensive intelligent analysis
 * @access Public
 */
router.post('/intelligent-analysis', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Intelligent analysis request received', { body: req.body });

  // Validate request
  const { error, value } = intelligentAnalysisSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Perform intelligent analysis
  const result = await intelligentAnalysisService.performIntelligentAnalysis(value);

  res.json({
    success: true,
    data: result,
    message: 'Intelligent analysis completed successfully'
  });
}));

/**
 * @route POST /api/ai/generate-visualization
 * @desc Generate enterprise visualization images
 * @access Public
 */
router.post('/generate-visualization', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Image generation request received', { body: req.body });

  // Validate request
  const { error, value } = imageGenerationSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Generate visualization
  const result = await imageGenerationService.generateEnterpriseVisualization(value);

  if (!result.success) {
    return res.status(500).json({
      success: false,
      message: 'Image generation failed',
      error: result.error
    });
  }

  res.json({
    success: true,
    data: result,
    message: 'Visualization generated successfully'
  });
}));

/**
 * @route POST /api/ai/generate-batch-visualization
 * @desc Generate multiple enterprise visualizations
 * @access Public
 */
router.post('/generate-batch-visualization', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Batch image generation request received');

  // Validate request
  const { error, value } = imageGenerationSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Generate batch visualizations
  const result = await imageGenerationService.generateEnterpriseBatch(value);

  res.json({
    success: true,
    data: result,
    message: 'Batch visualizations generated successfully'
  });
}));

/**
 * @route GET /api/ai/analysis-history/:enterpriseId
 * @desc Get enterprise analysis history
 * @access Public
 */
router.get('/analysis-history/:enterpriseId', asyncHandler(async (req: Request, res: Response) => {
  const { enterpriseId } = req.params;
  const { analysisType } = req.query;

  apiLogger.info('Analysis history request received', { enterpriseId, analysisType });

  const history = await intelligentAnalysisService.getEnterpriseAnalysisHistory(
    enterpriseId || '',
    analysisType as string
  );

  res.json({
    success: true,
    data: {
      enterpriseId,
      history,
      count: history.length
    }
  });
}));

/**
 * @route GET /api/ai/status
 * @desc Check AI service status
 * @access Public
 */
router.get('/status', asyncHandler(async (req: Request, res: Response) => {
  // Check image generation service status
  const imageServiceStatus = await imageGenerationService.checkServiceStatus();

  res.json({
    success: true,
    data: {
      status: 'operational',
      services: {
        deepseek: {
          available: !!process.env.DEEPSEEK_API_KEY,
          model: 'deepseek-reasoner'
        },
        backup: {
          available: !!process.env.BACKUP_AI_API_KEY,
          model: 'gpt-3.5-turbo'
        },
        tencent: {
          available: imageServiceStatus.available,
          configured: imageServiceStatus.configured,
          service: 'image-generation',
          error: imageServiceStatus.error
        },
        intelligentAnalysis: {
          available: true,
          features: ['profile', 'risk', 'investment', 'competitive', 'pathway']
        }
      },
      timestamp: new Date().toISOString()
    }
  });
}));

export { router as aiRoutes };
