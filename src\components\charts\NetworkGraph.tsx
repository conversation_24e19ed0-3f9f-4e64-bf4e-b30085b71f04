import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { ZoomIn, ZoomOut, RotateCcw, Download } from 'lucide-react';

interface Node {
  id: string;
  name: string;
  type: 'enterprise' | 'industry' | 'location';
  industry?: string;
  location?: string;
  score?: number;
  size?: number;
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
}

interface Link {
  source: string | Node;
  target: string | Node;
  type: string;
  strength?: number;
}

interface NetworkGraphProps {
  nodes: Node[];
  links: Link[];
  width?: number;
  height?: number;
  onNodeClick?: (node: Node) => void;
  onNodeHover?: (node: Node | null) => void;
}

const NetworkGraph: React.FC<NetworkGraphProps> = ({
  nodes,
  links,
  width = 800,
  height = 600,
  onNodeClick,
  onNodeHover
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [simulation, setSimulation] = useState<d3.Simulation<Node, Link> | null>(null);
  const [transform, setTransform] = useState(d3.zoomIdentity);

  useEffect(() => {
    if (!svgRef.current || nodes.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    // 创建缩放行为
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        setTransform(event.transform);
        g.attr('transform', event.transform);
      });

    svg.call(zoom);

    // 创建主容器
    const g = svg.append('g');

    // 创建力导向模拟
    const sim = d3.forceSimulation<Node>(nodes)
      .force('link', d3.forceLink<Node, Link>(links)
        .id(d => d.id)
        .distance(d => {
          const link = d as Link;
          return link.type === 'BELONGS_TO' ? 100 : 150;
        })
        .strength(d => (d as Link).strength || 0.5)
      )
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(d => (d.size || 20) + 5));

    setSimulation(sim);

    // 创建箭头标记
    const defs = svg.append('defs');
    defs.append('marker')
      .attr('id', 'arrowhead')
      .attr('viewBox', '-0 -5 10 10')
      .attr('refX', 25)
      .attr('refY', 0)
      .attr('orient', 'auto')
      .attr('markerWidth', 8)
      .attr('markerHeight', 8)
      .attr('xoverflow', 'visible')
      .append('svg:path')
      .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
      .attr('fill', '#64748b')
      .style('stroke', 'none');

    // 创建连线
    const link = g.append('g')
      .attr('class', 'links')
      .selectAll('line')
      .data(links)
      .enter().append('line')
      .attr('stroke', '#64748b')
      .attr('stroke-opacity', 0.6)
      .attr('stroke-width', d => Math.sqrt((d.strength || 0.5) * 4))
      .attr('marker-end', 'url(#arrowhead)');

    // 创建节点
    const node = g.append('g')
      .attr('class', 'nodes')
      .selectAll('g')
      .data(nodes)
      .enter().append('g')
      .attr('class', 'node')
      .call(d3.drag<SVGGElement, Node>()
        .on('start', (event, d) => {
          if (!event.active) sim.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on('drag', (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) sim.alphaTarget(0);
          d.fx = null;
          d.fy = null;
        })
      );

    // 添加节点圆圈
    node.append('circle')
      .attr('r', d => d.size || 20)
      .attr('fill', d => {
        switch (d.type) {
          case 'enterprise':
            return d.score && d.score > 85 ? '#10b981' : '#3b82f6';
          case 'industry':
            return '#f59e0b';
          case 'location':
            return '#8b5cf6';
          default:
            return '#64748b';
        }
      })
      .attr('stroke', '#1e293b')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer');

    // 添加节点标签
    node.append('text')
      .text(d => d.name)
      .attr('x', 0)
      .attr('y', d => (d.size || 20) + 15)
      .attr('text-anchor', 'middle')
      .attr('fill', '#e2e8f0')
      .attr('font-size', '12px')
      .attr('font-weight', 'bold')
      .style('pointer-events', 'none');

    // 添加节点交互
    node
      .on('click', (event, d) => {
        onNodeClick?.(d);
      })
      .on('mouseenter', (event, d) => {
        onNodeHover?.(d);
        // 高亮相关节点和连线
        const connectedNodes = new Set();
        links.forEach(link => {
          if (link.source === d.id || (typeof link.source === 'object' && link.source.id === d.id)) {
            connectedNodes.add(typeof link.target === 'string' ? link.target : link.target.id);
          }
          if (link.target === d.id || (typeof link.target === 'object' && link.target.id === d.id)) {
            connectedNodes.add(typeof link.source === 'string' ? link.source : link.source.id);
          }
        });

        node.style('opacity', n => n.id === d.id || connectedNodes.has(n.id) ? 1 : 0.3);
        link.style('opacity', l => {
          const sourceId = typeof l.source === 'string' ? l.source : l.source.id;
          const targetId = typeof l.target === 'string' ? l.target : l.target.id;
          return sourceId === d.id || targetId === d.id ? 1 : 0.1;
        });
      })
      .on('mouseleave', () => {
        onNodeHover?.(null);
        node.style('opacity', 1);
        link.style('opacity', 0.6);
      });

    // 更新位置
    sim.on('tick', () => {
      link
        .attr('x1', d => (d.source as Node).x!)
        .attr('y1', d => (d.source as Node).y!)
        .attr('x2', d => (d.target as Node).x!)
        .attr('y2', d => (d.target as Node).y!);

      node.attr('transform', d => `translate(${d.x},${d.y})`);
    });

    return () => {
      sim.stop();
    };
  }, [nodes, links, width, height, onNodeClick, onNodeHover]);

  const handleZoomIn = () => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().call(
        d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
        1.5
      );
    }
  };

  const handleZoomOut = () => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().call(
        d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
        1 / 1.5
      );
    }
  };

  const handleReset = () => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().call(
        d3.zoom<SVGSVGElement, unknown>().transform as any,
        d3.zoomIdentity
      );
    }
    if (simulation) {
      simulation.alpha(1).restart();
    }
  };

  const handleDownload = () => {
    if (svgRef.current) {
      const svgData = new XMLSerializer().serializeToString(svgRef.current);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      canvas.width = width;
      canvas.height = height;
      
      img.onload = () => {
        if (ctx) {
          ctx.fillStyle = '#0f172a'; // slate-900 background
          ctx.fillRect(0, 0, width, height);
          ctx.drawImage(img, 0, 0);
          
          const link = document.createElement('a');
          link.download = 'enterprise-network.png';
          link.href = canvas.toDataURL();
          link.click();
        }
      };
      
      img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
    }
  };

  return (
    <div className="relative bg-slate-900 rounded-lg overflow-hidden">
      {/* 控制按钮 */}
      <div className="absolute top-4 right-4 z-10 flex space-x-2">
        <button
          onClick={handleZoomIn}
          className="p-2 bg-slate-800 hover:bg-slate-700 text-white rounded-lg transition-colors"
          title="放大"
        >
          <ZoomIn className="w-4 h-4" />
        </button>
        <button
          onClick={handleZoomOut}
          className="p-2 bg-slate-800 hover:bg-slate-700 text-white rounded-lg transition-colors"
          title="缩小"
        >
          <ZoomOut className="w-4 h-4" />
        </button>
        <button
          onClick={handleReset}
          className="p-2 bg-slate-800 hover:bg-slate-700 text-white rounded-lg transition-colors"
          title="重置"
        >
          <RotateCcw className="w-4 h-4" />
        </button>
        <button
          onClick={handleDownload}
          className="p-2 bg-slate-800 hover:bg-slate-700 text-white rounded-lg transition-colors"
          title="下载"
        >
          <Download className="w-4 h-4" />
        </button>
      </div>

      {/* 图例 */}
      <div className="absolute top-4 left-4 z-10 bg-slate-800 rounded-lg p-3 text-sm">
        <div className="text-white font-medium mb-2">图例</div>
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-slate-300">企业</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
            <span className="text-slate-300">高分企业</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-amber-500"></div>
            <span className="text-slate-300">行业</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-violet-500"></div>
            <span className="text-slate-300">地区</span>
          </div>
        </div>
      </div>

      {/* SVG画布 */}
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="w-full h-full"
      />
    </div>
  );
};

export default NetworkGraph;
