import mongoose from 'mongoose';
import neo4j, { type Driver } from 'neo4j-driver';
import pkg from 'pg';
const { Pool } = pkg;
import { dbLogger } from '../utils/logger.js';

// MongoDB connection
export const connectMongoDB = async (): Promise<void> => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/enterprise_profiler';
    
    await mongoose.connect(mongoUri);
    
    dbLogger.info('✅ MongoDB connected successfully');
    
    // Handle connection events
    mongoose.connection.on('error', (error) => {
      dbLogger.error('MongoDB connection error:', error);
    });
    
    mongoose.connection.on('disconnected', () => {
      dbLogger.warn('MongoDB disconnected');
    });
    
  } catch (error) {
    dbLogger.error('Failed to connect to MongoDB:', error);
    throw error;
  }
};

// Neo4j connection
let neo4jDriver: Driver | null = null;

export const connectNeo4j = async (): Promise<Driver> => {
  try {
    const uri = process.env.NEO4J_URI || 'bolt://localhost:7687';
    const username = process.env.NEO4J_USERNAME || 'neo4j';
    const password = process.env.NEO4J_PASSWORD || 'password';
    
    neo4jDriver = neo4j.driver(uri, neo4j.auth.basic(username, password));
    
    // Test connection
    const session = neo4jDriver.session();
    await session.run('RETURN 1');
    await session.close();
    
    dbLogger.info('✅ Neo4j connected successfully');
    
    return neo4jDriver;
  } catch (error) {
    dbLogger.error('Failed to connect to Neo4j:', error);
    throw error;
  }
};

export const getNeo4jDriver = (): Driver => {
  if (!neo4jDriver) {
    throw new Error('Neo4j driver not initialized. Call connectNeo4j first.');
  }
  return neo4jDriver;
};

// PostgreSQL connection
let pgPool: pkg.Pool | null = null;

export const connectPostgreSQL = async (): Promise<pkg.Pool> => {
  try {
    pgPool = new Pool({
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      database: process.env.POSTGRES_DB || 'enterprise_analytics',
      user: process.env.POSTGRES_USER || 'postgres',
      password: process.env.POSTGRES_PASSWORD || 'password',
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });
    
    // Test connection
    const client = await pgPool.connect();
    await client.query('SELECT NOW()');
    client.release();
    
    dbLogger.info('✅ PostgreSQL connected successfully');
    
    return pgPool;
  } catch (error) {
    dbLogger.error('Failed to connect to PostgreSQL:', error);
    throw error;
  }
};

export const getPostgreSQLPool = (): pkg.Pool => {
  if (!pgPool) {
    throw new Error('PostgreSQL pool not initialized. Call connectPostgreSQL first.');
  }
  return pgPool;
};

// Connect all databases
export const connectDatabases = async (): Promise<void> => {
  dbLogger.info('🔌 Connecting to databases...');

  try {
    await Promise.all([
      connectMongoDB(),
      connectNeo4j(),
      connectPostgreSQL()
    ]);

    // Initialize PostgreSQL tables
    const { postgresService } = await import('../services/postgresService.js');
    await postgresService.initializeTables();

    dbLogger.info('🎉 All databases connected and initialized successfully');
  } catch (error) {
    dbLogger.error('Failed to connect to databases:', error);
    throw error;
  }
};

// Graceful shutdown
export const closeDatabaseConnections = async (): Promise<void> => {
  dbLogger.info('🔌 Closing database connections...');
  
  try {
    // Close MongoDB
    await mongoose.connection.close();
    
    // Close Neo4j
    if (neo4jDriver) {
      await neo4jDriver.close();
    }
    
    // Close PostgreSQL
    if (pgPool) {
      await pgPool.end();
    }
    
    dbLogger.info('✅ All database connections closed');
  } catch (error) {
    dbLogger.error('Error closing database connections:', error);
  }
};
