#!/usr/bin/env node

import http from 'http';
import https from 'https';
import fs from 'fs';
import path from 'path';
import net from 'net';

console.log('🔍 企业画像师平台状态检查');
console.log('============================');

// 检查URL的函数
function checkUrl(url, name) {
  return new Promise((resolve) => {
    const client = url.startsWith('https') ? https : http;
    const request = client.get(url, (res) => {
      const statusCode = res.statusCode;
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (statusCode === 200) {
          console.log(`✅ ${name}: 正常 (${statusCode})`);
          try {
            const jsonData = JSON.parse(data);
            if (jsonData.success !== undefined) {
              console.log(`   响应: ${jsonData.success ? '成功' : '失败'}`);
            }
          } catch (e) {
            // 不是JSON响应，可能是HTML
            if (data.includes('企业画像师')) {
              console.log('   内容: 包含企业画像师标题 ✅');
            }
          }
        } else {
          console.log(`⚠️  ${name}: 状态码 ${statusCode}`);
        }
        resolve(statusCode === 200);
      });
    });
    
    request.on('error', (err) => {
      console.log(`❌ ${name}: 连接失败 - ${err.message}`);
      resolve(false);
    });
    
    request.setTimeout(5000, () => {
      console.log(`⏰ ${name}: 请求超时`);
      request.destroy();
      resolve(false);
    });
  });
}

// 检查端口是否被占用
function checkPort(port, name) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, () => {
      server.close(() => {
        console.log(`✅ 端口 ${port} (${name}): 可用`);
        resolve(true);
      });
    });
    
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.log(`🔄 端口 ${port} (${name}): 被占用`);
        resolve(false);
      } else {
        console.log(`❌ 端口 ${port} (${name}): 错误 - ${err.message}`);
        resolve(false);
      }
    });
  });
}

async function main() {
  console.log('📋 检查端口状态...');
  const frontendPortFree = await checkPort(5173, '前端');
  const backendPortFree = await checkPort(3001, '后端');
  
  console.log('\n📋 检查服务状态...');
  
  // 如果端口被占用，说明服务可能在运行，尝试访问
  if (!frontendPortFree) {
    await checkUrl('http://localhost:5173', '前端服务');
  }
  
  if (!backendPortFree) {
    await checkUrl('http://localhost:3001/health', '后端健康检查');
    await checkUrl('http://localhost:3001/api/ai/status', 'AI服务状态');
    await checkUrl('http://localhost:3001/api/enterprises', '企业API');
  }
  
  console.log('\n📋 检查文件结构...');
  
  const checkFile = (filePath, name) => {
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${name}: 存在`);
      return true;
    } else {
      console.log(`❌ ${name}: 不存在`);
      return false;
    }
  };
  
  checkFile('src/App.tsx', '前端主文件');
  checkFile('server/src/app.ts', '后端主文件');
  checkFile('server/.env', '环境配置文件');
  checkFile('package.json', '前端package.json');
  checkFile('server/package.json', '后端package.json');
  
  console.log('\n📋 检查依赖安装...');
  checkFile('node_modules', '前端依赖');
  checkFile('server/node_modules', '后端依赖');
  
  console.log('\n📋 系统建议...');
  
  if (frontendPortFree && backendPortFree) {
    console.log('💡 所有端口都可用，可以启动服务');
    console.log('   运行: npm run dev 或 start-dev.bat');
  } else if (!frontendPortFree && !backendPortFree) {
    console.log('💡 服务似乎正在运行');
    console.log('   访问: http://localhost:5173');
  } else {
    console.log('💡 部分服务在运行，建议重新启动');
    console.log('   运行: force-clean-start.bat 或 force-clean-start.sh');
  }
  
  console.log('\n============================');
  console.log('检查完成！');
}

main().catch(console.error);
