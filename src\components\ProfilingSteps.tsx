import React, { useState } from 'react';
import { Target, Search, TrendingUp, Award, CheckCircle, ArrowRight, Building2, Users, MapPin } from 'lucide-react';

const ProfilingSteps = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    targetConditions: '',
    searchMethods: '',
    salesAcceleration: '',
    valueRealization: ''
  });

  const steps = [
    {
      id: 1,
      title: '明确目标客户条件',
      description: '确定理想客户应具备的行业、规模、发展阶段等特征',
      icon: Target,
      color: 'blue',
      fields: [
        { label: '目标行业', placeholder: '如：互联网、制造业、金融服务等' },
        { label: '企业规模', placeholder: '如：100-500人、500-1000人等' },
        { label: '发展阶段', placeholder: '如：初创期、成长期、成熟期等' },
        { label: '地理位置', placeholder: '如：北京、上海、深圳等' },
      ]
    },
    {
      id: 2,
      title: '寻找合适企业的方法',
      description: '利用公开数据获取企业信息，构建企业数据库',
      icon: Search,
      color: 'green',
      fields: [
        { label: '数据源选择', placeholder: '如：企查查、天眼查、官网等' },
        { label: '关键词设置', placeholder: '如：技术关键词、业务关键词等' },
        { label: '筛选条件', placeholder: '如：成立年限、注册资本等' },
        { label: '数据更新频率', placeholder: '如：每日、每周、每月等' },
      ]
    },
    {
      id: 3,
      title: '加速销售流程',
      description: '根据画像精准匹配客户，提升销售转化效率',
      icon: TrendingUp,
      color: 'purple',
      fields: [
        { label: '匹配算法', placeholder: '如：相似度匹配、决策树匹配等' },
        { label: '优先级排序', placeholder: '如：按匹配度、按潜在价值等' },
        { label: '接触策略', placeholder: '如：邮件、电话、社交媒体等' },
        { label: '跟进计划', placeholder: '如：接触频率、内容策略等' },
      ]
    },
    {
      id: 4,
      title: '实现产品价值落地',
      description: '在匹配度高的企业中深耕，推动产品价值最大化',
      icon: Award,
      color: 'orange',
      fields: [
        { label: '价值主张', placeholder: '如：降本增效、提升竞争力等' },
        { label: '成功案例', placeholder: '如：类似客户的成功经验等' },
        { label: 'ROI计算', placeholder: '如：投资回报率、成本节约等' },
        { label: '长期合作', placeholder: '如：续约策略、增值服务等' },
      ]
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-500',
      green: 'bg-green-500',
      purple: 'bg-purple-500',
      orange: 'bg-orange-500',
    };
    return colors[color as keyof typeof colors] || 'bg-gray-500';
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentStepData = steps[currentStep - 1];
  const Icon = currentStepData.icon;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">企业画像绘制流程</h1>
        <p className="text-slate-400">四步骤系统化绘制精准企业画像</p>
      </div>

      {/* Progress Bar */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <div className="flex items-center justify-between mb-4">
          {steps.map((step, index) => {
            const StepIcon = step.icon;
            return (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full ${
                  step.id <= currentStep ? getColorClasses(step.color) : 'bg-slate-600'
                } text-white font-semibold`}>
                  {step.id < currentStep ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : (
                    <StepIcon className="w-6 h-6" />
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-20 h-1 ml-4 ${
                    step.id < currentStep ? 'bg-green-500' : 'bg-slate-600'
                  }`}></div>
                )}
              </div>
            );
          })}
        </div>
        <div className="text-center">
          <span className="text-slate-400">步骤 {currentStep} / {steps.length}</span>
        </div>
      </div>

      {/* Current Step Content */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <div className="flex items-center space-x-4 mb-6">
          <div className={`w-16 h-16 rounded-xl ${getColorClasses(currentStepData.color)} flex items-center justify-center`}>
            <Icon className="w-8 h-8 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold text-white">{currentStepData.title}</h2>
            <p className="text-slate-400 mt-1">{currentStepData.description}</p>
          </div>
        </div>

        {/* Form Fields */}
        <div className="space-y-4">
          {currentStepData.fields.map((field, index) => (
            <div key={index}>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                {field.label}
              </label>
              <input
                type="text"
                placeholder={field.placeholder}
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between mt-8">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="px-6 py-3 bg-slate-700 hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors duration-200"
          >
            上一步
          </button>
          
          {currentStep < steps.length ? (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
            >
              <span>下一步</span>
              <ArrowRight className="w-4 h-4" />
            </button>
          ) : (
            <button className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors duration-200">
              完成画像绘制
            </button>
          )}
        </div>
      </div>

      {/* Three Dimensions Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-3 mb-4">
            <Building2 className="w-6 h-6 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">企业信息</h3>
          </div>
          <p className="text-slate-400 text-sm">
            企业性质、行业、经营年限、发展阶段、行业地位、经营风格等关键信息
          </p>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-3 mb-4">
            <Users className="w-6 h-6 text-green-400" />
            <h3 className="text-lg font-semibold text-white">角色信息</h3>
          </div>
          <p className="text-slate-400 text-sm">
            使用者、影响者、决策者三个关键岗位角色的背景、目标与痛点分析
          </p>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-3 mb-4">
            <MapPin className="w-6 h-6 text-purple-400" />
            <h3 className="text-lg font-semibold text-white">用户路径</h3>
          </div>
          <p className="text-slate-400 text-sm">
            企业需求产生原因、决策过程、选型逻辑和预期达成情况分析
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProfilingSteps;