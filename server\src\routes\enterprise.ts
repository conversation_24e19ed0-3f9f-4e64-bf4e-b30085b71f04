import { Router, type Request, type Response } from 'express';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler.js';
import { apiLogger } from '../utils/logger.js';
import { enterpriseService } from '../services/enterpriseService.js';
import Joi from 'joi';

const router = Router();

// Validation schemas
const enterpriseQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  industry: Joi.string().optional(),
  size: Joi.string().optional(),
  stage: Joi.string().optional(),
  location: Joi.string().optional(),
  search: Joi.string().optional(),
  minScore: Joi.number().min(0).max(100).optional(),
  maxScore: Joi.number().min(0).max(100).optional(),
  status: Joi.string().optional()
});

const createEnterpriseSchema = Joi.object({
  name: Joi.string().required().min(1).max(200),
  industry: Joi.string().required().max(100),
  size: Joi.string().optional(),
  stage: Joi.string().optional(),
  location: Joi.string().optional(),
  revenue: Joi.string().optional(),
  employees: Joi.number().integer().min(0).optional(),
  founded: Joi.number().integer().min(1800).max(new Date().getFullYear()).optional(),
  description: Joi.string().optional().max(1000),
  website: Joi.string().uri().optional(),
  contactInfo: Joi.object({
    email: Joi.string().email().optional(),
    phone: Joi.string().optional(),
    address: Joi.string().max(500).optional()
  }).optional(),
  businessInfo: Joi.object({
    registrationNumber: Joi.string().optional(),
    legalRepresentative: Joi.string().max(100).optional(),
    registeredCapital: Joi.string().optional(),
    businessScope: Joi.string().max(1000).optional()
  }).optional()
});

/**
 * @route GET /api/enterprises
 * @desc Get enterprises list with filtering and pagination
 * @access Public
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Get enterprises request received', { query: req.query });

  // Validate query parameters
  const { error, value } = enterpriseQuerySchema.validate(req.query);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Get enterprises from database
  const result = await enterpriseService.getEnterprises(value);

  res.json({
    success: true,
    data: result
  });
}));

/**
 * @route GET /api/enterprises/:id
 * @desc Get single enterprise by ID
 * @access Public
 */
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  apiLogger.info('Get enterprise by ID request received', { id });

  const enterprise = await enterpriseService.getEnterpriseById(id || '');

  res.json({
    success: true,
    data: enterprise
  });
}));

/**
 * @route POST /api/enterprises
 * @desc Create new enterprise
 * @access Public
 */
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Create enterprise request received', { body: req.body });

  // Validate request
  const { error, value } = createEnterpriseSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      details: error.details
    });
  }

  // Create new enterprise
  const enterprise = await enterpriseService.createEnterprise(value);

  res.status(201).json({
    success: true,
    data: enterprise,
    message: 'Enterprise created successfully'
  });
}));

/**
 * @route GET /api/enterprises/stats/overview
 * @desc Get enterprises statistics overview
 * @access Public
 */
router.get('/stats/overview', asyncHandler(async (req: Request, res: Response) => {
  apiLogger.info('Get enterprises stats request received');

  const stats = await enterpriseService.getEnterpriseStats();

  res.json({
    success: true,
    data: stats
  });
}));

export { router as enterpriseRoutes };
