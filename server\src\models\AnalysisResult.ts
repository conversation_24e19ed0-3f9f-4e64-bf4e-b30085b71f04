import mongoose, { Document, Schema } from 'mongoose';

export interface IAnalysisResult extends Document {
  enterpriseId: mongoose.Types.ObjectId;
  profileId?: mongoose.Types.ObjectId;
  analysisType: 'enterprise_profile' | 'role_analysis' | 'pathway_analysis' | 'matching_analysis' | 'risk_assessment';
  aiModel: string;
  prompt?: string;
  rawResponse?: any;
  processedData: {
    overallScore?: number;
    dimensions?: Array<{
      name: string;
      score: number;
      factors: string[];
      recommendations: string[];
    }>;
    insights?: string[];
    risks?: Array<{
      type: string;
      level: 'low' | 'medium' | 'high';
      description: string;
      mitigation?: string;
    }>;
    recommendations?: Array<{
      category: string;
      priority: 'low' | 'medium' | 'high';
      suggestion: string;
      expectedImpact?: string;
    }>;
    nextSteps?: string[];
    metadata?: any;
  };
  confidence: number;
  processingTime?: number;
  tokensUsed?: {
    prompt: number;
    completion: number;
    total: number;
  };
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

const AnalysisResultSchema = new Schema<IAnalysisResult>({
  enterpriseId: {
    type: Schema.Types.ObjectId,
    ref: 'Enterprise',
    required: true,
    index: true
  },
  profileId: {
    type: Schema.Types.ObjectId,
    ref: 'Profile',
    index: true
  },
  analysisType: {
    type: String,
    required: true,
    enum: ['enterprise_profile', 'role_analysis', 'pathway_analysis', 'matching_analysis', 'risk_assessment'],
    index: true
  },
  aiModel: {
    type: String,
    required: true,
    default: 'deepseek-reasoner'
  },
  prompt: {
    type: String,
    maxlength: 5000
  },
  rawResponse: {
    type: Schema.Types.Mixed
  },
  processedData: {
    overallScore: {
      type: Number,
      min: 0,
      max: 100
    },
    dimensions: [{
      name: {
        type: String,
        required: true
      },
      score: {
        type: Number,
        min: 0,
        max: 100,
        required: true
      },
      factors: [{
        type: String
      }],
      recommendations: [{
        type: String
      }]
    }],
    insights: [{
      type: String,
      maxlength: 1000
    }],
    risks: [{
      type: {
        type: String,
        required: true
      },
      level: {
        type: String,
        enum: ['low', 'medium', 'high'],
        required: true
      },
      description: {
        type: String,
        required: true,
        maxlength: 500
      },
      mitigation: {
        type: String,
        maxlength: 500
      }
    }],
    recommendations: [{
      category: {
        type: String,
        required: true
      },
      priority: {
        type: String,
        enum: ['low', 'medium', 'high'],
        required: true
      },
      suggestion: {
        type: String,
        required: true,
        maxlength: 500
      },
      expectedImpact: {
        type: String,
        maxlength: 300
      }
    }],
    nextSteps: [{
      type: String,
      maxlength: 300
    }],
    metadata: {
      type: Schema.Types.Mixed
    }
  },
  confidence: {
    type: Number,
    required: true,
    min: 0,
    max: 1,
    default: 0.5
  },
  processingTime: {
    type: Number,
    min: 0
  },
  tokensUsed: {
    prompt: {
      type: Number,
      min: 0
    },
    completion: {
      type: Number,
      min: 0
    },
    total: {
      type: Number,
      min: 0
    }
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending',
    index: true
  },
  error: {
    type: String,
    maxlength: 1000
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
AnalysisResultSchema.index({ enterpriseId: 1, analysisType: 1 });
AnalysisResultSchema.index({ profileId: 1, createdAt: -1 });
AnalysisResultSchema.index({ status: 1, createdAt: -1 });
AnalysisResultSchema.index({ 'processedData.overallScore': -1 });

// 虚拟字段
AnalysisResultSchema.virtual('isRecent').get(function() {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return this.createdAt > oneDayAgo;
});

// 静态方法
AnalysisResultSchema.statics.findByEnterprise = function(enterpriseId: string) {
  return this.find({ enterpriseId }).sort({ createdAt: -1 });
};

AnalysisResultSchema.statics.findByType = function(analysisType: string) {
  return this.find({ analysisType, status: 'completed' }).sort({ createdAt: -1 });
};

AnalysisResultSchema.statics.findHighConfidence = function(threshold: number = 0.8) {
  return this.find({ confidence: { $gte: threshold }, status: 'completed' });
};

// 实例方法
AnalysisResultSchema.methods.markCompleted = function(processedData: any, confidence: number = 0.8) {
  this.processedData = processedData;
  this.confidence = confidence;
  this.status = 'completed';
  return this.save();
};

AnalysisResultSchema.methods.markFailed = function(error: string) {
  this.error = error;
  this.status = 'failed';
  return this.save();
};

export const AnalysisResult = mongoose.model<IAnalysisResult>('AnalysisResult', AnalysisResultSchema);
