import axios from 'axios';
import { aiLogger } from '../utils/logger.js';
import { CustomError } from '../middleware/errorHandler.js';

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface EnterpriseAnalysisRequest {
  companyName: string;
  industry?: string;
  description?: string;
  targetConditions?: {
    industry?: string;
    size?: string;
    stage?: string;
    location?: string;
  };
}

export class AIService {
  private deepseekApiUrl: string;
  private deepseekApiKey: string;
  private backupApiUrl: string;
  private backupApiKey: string;

  constructor() {
    this.deepseekApiUrl = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com';
    this.deepseekApiKey = process.env.DEEPSEEK_API_KEY || '';
    this.backupApiUrl = process.env.BACKUP_AI_API_URL || 'https://vip.apiyi.com/v1';
    this.backupApiKey = process.env.BACKUP_AI_API_KEY || '';

    if (!this.deepseekApiKey) {
      aiLogger.warn('DEEPSEEK_API_KEY not found, AI features may not work properly');
    }
  }

  /**
   * Call DeepSeek API with fallback to backup service
   */
  private async callAI(messages: any[], usePrimary: boolean = true): Promise<AIResponse> {
    const apiUrl = usePrimary ? this.deepseekApiUrl : this.backupApiUrl;
    const apiKey = usePrimary ? this.deepseekApiKey : this.backupApiKey;
    const model = usePrimary ? 'deepseek-reasoner' : 'gpt-3.5-turbo';

    try {
      aiLogger.info(`Calling ${usePrimary ? 'DeepSeek' : 'Backup'} AI API`);

      const response = await axios.post(
        `${apiUrl}/chat/completions`,
        {
          model,
          messages,
          temperature: 0.7,
          max_tokens: 2000,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const result = response.data;
      
      aiLogger.info('AI API call successful', {
        model,
        usage: result.usage,
        service: usePrimary ? 'deepseek' : 'backup'
      });

      return {
        success: true,
        data: result.choices[0]?.message?.content,
        usage: result.usage
      };

    } catch (error: any) {
      aiLogger.error(`${usePrimary ? 'DeepSeek' : 'Backup'} AI API error:`, error.response?.data || error.message);
      
      // If primary service fails, try backup
      if (usePrimary && this.backupApiKey) {
        aiLogger.info('Falling back to backup AI service');
        return this.callAI(messages, false);
      }

      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Analyze enterprise profile using AI
   */
  async analyzeEnterprise(request: EnterpriseAnalysisRequest): Promise<AIResponse> {
    const prompt = this.buildEnterpriseAnalysisPrompt(request);

    const messages = [
      {
        role: 'system',
        content: `你是一个专业的企业画像分析师，具有深厚的商业分析和市场洞察能力。
        你的任务是基于提供的企业信息，进行全面的企业画像分析。

        请严格按照以下JSON格式返回分析结果：
        {
          "overallScore": 85,
          "dimensions": [
            {
              "name": "业务成熟度",
              "score": 88,
              "factors": ["成立时间较长", "业务模式成熟"],
              "recommendations": ["关注新业务拓展机会"]
            }
          ],
          "insights": ["该企业正处于快速发展期"],
          "risks": [
            {
              "type": "市场风险",
              "level": "medium",
              "description": "行业竞争激烈",
              "mitigation": "持续创新保持优势"
            }
          ],
          "recommendations": [
            {
              "category": "合作策略",
              "priority": "high",
              "suggestion": "建立长期合作关系",
              "expectedImpact": "提升市场份额"
            }
          ],
          "nextSteps": ["安排技术交流会议"],
          "confidence": 0.85
        }`
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    return this.callAI(messages);
  }

  /**
   * Generate intelligent matching recommendations
   */
  async generateMatchingRecommendations(enterprises: any[], targetConditions: any): Promise<AIResponse> {
    const prompt = `
    基于以下目标客户条件和企业列表，请进行智能匹配分析：
    
    目标条件：
    ${JSON.stringify(targetConditions, null, 2)}
    
    企业列表：
    ${JSON.stringify(enterprises, null, 2)}
    
    请分析每个企业与目标条件的匹配度，并提供：
    1. 匹配评分 (0-100)
    2. 匹配原因
    3. 潜在价值评估
    4. 接触建议
    5. 风险评估
    
    返回JSON格式的分析结果。
    `;

    const messages = [
      {
        role: 'system',
        content: '你是一个智能客户匹配专家，擅长分析企业特征并提供精准的匹配建议。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    return this.callAI(messages);
  }

  /**
   * Analyze user decision pathway
   */
  async analyzeUserPathway(enterpriseData: any, roleData: any): Promise<AIResponse> {
    const prompt = `
    基于企业信息和角色数据，分析用户决策路径：
    
    企业信息：
    ${JSON.stringify(enterpriseData, null, 2)}
    
    角色信息：
    ${JSON.stringify(roleData, null, 2)}
    
    请分析：
    1. 决策流程各阶段
    2. 关键决策点
    3. 影响因素
    4. 转化瓶颈
    5. 优化建议
    
    返回结构化的分析结果。
    `;

    const messages = [
      {
        role: 'system',
        content: '你是一个用户行为分析专家，专注于B2B决策路径分析。'
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    return this.callAI(messages);
  }

  /**
   * 深度企业分析（包含竞争对手分析）
   */
  async deepAnalyzeEnterprise(enterpriseData: any, competitors: any[] = []): Promise<AIResponse> {
    const messages = [
      {
        role: 'system',
        content: `你是一个资深的企业战略分析师，专门进行深度企业分析。
        请基于企业信息和竞争对手数据，进行全面的战略分析。

        返回JSON格式，包含：
        1. 企业核心竞争力分析
        2. 市场定位和差异化优势
        3. 竞争格局分析
        4. 发展机会和威胁
        5. 战略建议和行动计划`
      },
      {
        role: 'user',
        content: `
        目标企业信息：
        ${JSON.stringify(enterpriseData, null, 2)}

        主要竞争对手：
        ${JSON.stringify(competitors, null, 2)}

        请进行深度分析并提供战略建议。
        `
      }
    ];

    return this.callAI(messages);
  }

  /**
   * 智能风险评估
   */
  async assessRisks(enterpriseData: any, industryTrends: any = {}): Promise<AIResponse> {
    const messages = [
      {
        role: 'system',
        content: `你是一个专业的风险评估专家，擅长识别企业经营中的各类风险。
        请基于企业信息和行业趋势，进行全面的风险评估。

        返回JSON格式，包含：
        {
          "riskLevel": "low|medium|high",
          "riskCategories": [
            {
              "category": "财务风险",
              "level": "medium",
              "probability": 0.3,
              "impact": "medium",
              "description": "具体风险描述",
              "indicators": ["风险指标1", "风险指标2"],
              "mitigation": "缓解措施"
            }
          ],
          "overallAssessment": "整体风险评估",
          "monitoringPoints": ["需要监控的关键指标"],
          "recommendations": ["风险管理建议"]
        }`
      },
      {
        role: 'user',
        content: `
        企业信息：
        ${JSON.stringify(enterpriseData, null, 2)}

        行业趋势：
        ${JSON.stringify(industryTrends, null, 2)}

        请进行全面的风险评估。
        `
      }
    ];

    return this.callAI(messages);
  }

  /**
   * 投资价值评估
   */
  async evaluateInvestmentValue(enterpriseData: any, financialData: any = {}): Promise<AIResponse> {
    const messages = [
      {
        role: 'system',
        content: `你是一个专业的投资分析师，专门评估企业的投资价值。
        请基于企业信息和财务数据，进行投资价值分析。

        返回JSON格式，包含：
        {
          "investmentScore": 85,
          "valuation": {
            "estimatedValue": "预估企业价值",
            "priceRange": {"min": 100000000, "max": 150000000},
            "valuationMethod": "估值方法"
          },
          "strengths": ["投资亮点"],
          "concerns": ["投资顾虑"],
          "growthPotential": {
            "score": 90,
            "drivers": ["增长驱动因素"],
            "timeline": "预期增长时间线"
          },
          "recommendation": "buy|hold|sell",
          "targetPrice": 120000000,
          "riskFactors": ["投资风险因素"]
        }`
      },
      {
        role: 'user',
        content: `
        企业信息：
        ${JSON.stringify(enterpriseData, null, 2)}

        财务数据：
        ${JSON.stringify(financialData, null, 2)}

        请进行投资价值评估。
        `
      }
    ];

    return this.callAI(messages);
  }

  private buildEnterpriseAnalysisPrompt(request: EnterpriseAnalysisRequest): string {
    return `
    请分析以下企业信息：

    企业名称：${request.companyName}
    所属行业：${request.industry || '未知'}
    企业描述：${request.description || '无'}

    目标客户条件：
    ${request.targetConditions ? JSON.stringify(request.targetConditions, null, 2) : '无特定条件'}

    请提供详细的企业画像分析，包括企业特征、市场地位、发展潜力等维度。
    `;
  }
}

// Export a function to get the AI service instance
export const getAIService = () => new AIService();
