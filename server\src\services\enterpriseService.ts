import { Enterprise, type IEnterprise } from '../models/Enterprise.js';
import { AnalysisResult } from '../models/AnalysisResult.js';
import { dbLogger } from '../utils/logger.js';
import { CustomError } from '../middleware/errorHandler.js';

export interface EnterpriseQuery {
  page?: number;
  limit?: number;
  industry?: string;
  size?: string;
  stage?: string;
  location?: string;
  search?: string;
  minScore?: number;
  maxScore?: number;
  status?: string;
}

export interface EnterpriseStats {
  total: number;
  byIndustry: Record<string, number>;
  byStage: Record<string, number>;
  byLocation: Record<string, number>;
  bySize: Record<string, number>;
  averageScore: number;
  recentlyAnalyzed: number;
}

export class EnterpriseService {
  /**
   * 获取企业列表（支持筛选和分页）
   */
  async getEnterprises(query: EnterpriseQuery) {
    try {
      const {
        page = 1,
        limit = 20,
        industry,
        size,
        stage,
        location,
        search,
        minScore,
        maxScore,
        status
      } = query;

      // 构建查询条件
      const filter: any = {};

      if (industry) {
        filter.industry = new RegExp(industry, 'i');
      }

      if (size) {
        filter.size = size;
      }

      if (stage) {
        filter.stage = stage;
      }

      if (location) {
        filter.location = new RegExp(location, 'i');
      }

      if (status) {
        filter.status = status;
      }

      if (minScore !== undefined || maxScore !== undefined) {
        filter.score = {};
        if (minScore !== undefined) filter.score.$gte = minScore;
        if (maxScore !== undefined) filter.score.$lte = maxScore;
      }

      if (search) {
        filter.$or = [
          { name: new RegExp(search, 'i') },
          { description: new RegExp(search, 'i') },
          { 'businessInfo.businessScope': new RegExp(search, 'i') }
        ];
      }

      // 执行查询
      const skip = (page - 1) * limit;
      const [enterprises, total] = await Promise.all([
        Enterprise.find(filter)
          .sort({ score: -1, updatedAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Enterprise.countDocuments(filter)
      ]);

      dbLogger.info('Retrieved enterprises', { 
        count: enterprises.length, 
        total, 
        page, 
        limit,
        filters: Object.keys(filter)
      });

      return {
        enterprises,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNext: skip + limit < total,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      dbLogger.error('Failed to get enterprises:', error);
      throw new CustomError('获取企业列表失败', 500);
    }
  }

  /**
   * 根据ID获取单个企业
   */
  async getEnterpriseById(id: string): Promise<IEnterprise | null> {
    try {
      const enterprise = await Enterprise.findById(id);
      
      if (!enterprise) {
        throw new CustomError('企业不存在', 404);
      }

      dbLogger.info('Retrieved enterprise by ID', { id, name: enterprise.name });
      return enterprise;
    } catch (error) {
      if (error instanceof CustomError) throw error;
      dbLogger.error('Failed to get enterprise by ID:', error);
      throw new CustomError('获取企业信息失败', 500);
    }
  }

  /**
   * 创建新企业
   */
  async createEnterprise(enterpriseData: Partial<IEnterprise>): Promise<IEnterprise> {
    try {
      // 检查企业名称是否已存在
      const existingEnterprise = await Enterprise.findOne({ name: enterpriseData.name });
      if (existingEnterprise) {
        throw new CustomError('企业名称已存在', 409);
      }

      // 检查注册号是否已存在
      if (enterpriseData.businessInfo?.registrationNumber) {
        const existingByRegNum = await Enterprise.findOne({
          'businessInfo.registrationNumber': enterpriseData.businessInfo.registrationNumber
        });
        if (existingByRegNum) {
          throw new CustomError('企业注册号已存在', 409);
        }
      }

      const enterprise = new Enterprise(enterpriseData);
      await enterprise.save();

      dbLogger.info('Created new enterprise', { id: enterprise._id, name: enterprise.name });
      return enterprise;
    } catch (error) {
      if (error instanceof CustomError) throw error;
      dbLogger.error('Failed to create enterprise:', error);
      throw new CustomError('创建企业失败', 500);
    }
  }

  /**
   * 更新企业信息
   */
  async updateEnterprise(id: string, updateData: Partial<IEnterprise>): Promise<IEnterprise | null> {
    try {
      const enterprise = await Enterprise.findByIdAndUpdate(
        id,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      );

      if (!enterprise) {
        throw new CustomError('企业不存在', 404);
      }

      dbLogger.info('Updated enterprise', { id, name: enterprise.name });
      return enterprise;
    } catch (error) {
      if (error instanceof CustomError) throw error;
      dbLogger.error('Failed to update enterprise:', error);
      throw new CustomError('更新企业信息失败', 500);
    }
  }

  /**
   * 删除企业
   */
  async deleteEnterprise(id: string): Promise<boolean> {
    try {
      const enterprise = await Enterprise.findByIdAndDelete(id);
      
      if (!enterprise) {
        throw new CustomError('企业不存在', 404);
      }

      // 删除相关的分析结果
      await AnalysisResult.deleteMany({ enterpriseId: id });

      dbLogger.info('Deleted enterprise and related data', { id, name: enterprise.name });
      return true;
    } catch (error) {
      if (error instanceof CustomError) throw error;
      dbLogger.error('Failed to delete enterprise:', error);
      throw new CustomError('删除企业失败', 500);
    }
  }

  /**
   * 获取企业统计信息
   */
  async getEnterpriseStats(): Promise<EnterpriseStats> {
    try {
      const [
        total,
        industryStats,
        stageStats,
        locationStats,
        sizeStats,
        scoreStats,
        recentAnalyzed
      ] = await Promise.all([
        Enterprise.countDocuments(),
        Enterprise.aggregate([
          { $group: { _id: '$industry', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),
        Enterprise.aggregate([
          { $group: { _id: '$stage', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),
        Enterprise.aggregate([
          { $group: { _id: '$location', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),
        Enterprise.aggregate([
          { $group: { _id: '$size', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),
        Enterprise.aggregate([
          { $group: { _id: null, avgScore: { $avg: '$score' } } }
        ]),
        Enterprise.countDocuments({
          'analysisData.lastAnalyzed': {
            $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
          }
        })
      ]);

      const stats: EnterpriseStats = {
        total,
        byIndustry: industryStats.reduce((acc, item) => {
          acc[item._id || '未知'] = item.count;
          return acc;
        }, {}),
        byStage: stageStats.reduce((acc, item) => {
          acc[item._id || '未知'] = item.count;
          return acc;
        }, {}),
        byLocation: locationStats.reduce((acc, item) => {
          acc[item._id || '未知'] = item.count;
          return acc;
        }, {}),
        bySize: sizeStats.reduce((acc, item) => {
          acc[item._id || '未知'] = item.count;
          return acc;
        }, {}),
        averageScore: scoreStats[0]?.avgScore || 0,
        recentlyAnalyzed: recentAnalyzed
      };

      dbLogger.info('Generated enterprise statistics', { total: stats.total });
      return stats;
    } catch (error) {
      dbLogger.error('Failed to get enterprise stats:', error);
      throw new CustomError('获取企业统计信息失败', 500);
    }
  }

  /**
   * 批量导入企业
   */
  async bulkImportEnterprises(enterprises: Partial<IEnterprise>[]): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    const result = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const enterpriseData of enterprises) {
      try {
        await this.createEnterprise(enterpriseData);
        result.success++;
      } catch (error) {
        result.failed++;
        result.errors.push(`${enterpriseData.name}: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    dbLogger.info('Bulk import completed', result);
    return result;
  }

  /**
   * 搜索企业（全文搜索）
   */
  async searchEnterprises(searchTerm: string, limit: number = 10): Promise<IEnterprise[]> {
    try {
      const enterprises = await Enterprise.find(
        { $text: { $search: searchTerm } },
        { score: { $meta: 'textScore' } }
      )
      .sort({ score: { $meta: 'textScore' } })
      .limit(limit);

      dbLogger.info('Search completed', { searchTerm, results: enterprises.length });
      return enterprises;
    } catch (error) {
      dbLogger.error('Search failed:', error);
      throw new CustomError('搜索企业失败', 500);
    }
  }
}

export const enterpriseService = new EnterpriseService();
