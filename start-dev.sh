#!/bin/bash

echo "🚀 启动企业画像师平台开发环境"
echo "================================"

# 检查Node.js版本
echo "📋 检查环境..."
node_version=$(node -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Node.js版本: $node_version"
else
    echo "❌ 未找到Node.js，请先安装Node.js 18+"
    exit 1
fi

# 检查npm版本
npm_version=$(npm -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ npm版本: $npm_version"
else
    echo "❌ 未找到npm"
    exit 1
fi

# 安装前端依赖
echo ""
echo "📦 安装前端依赖..."
if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 前端依赖安装失败"
        exit 1
    fi
else
    echo "✅ 前端依赖已存在"
fi

# 安装后端依赖
echo ""
echo "📦 安装后端依赖..."
cd server
if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 后端依赖安装失败"
        exit 1
    fi
else
    echo "✅ 后端依赖已存在"
fi
cd ..

# 检查环境变量文件
echo ""
echo "🔧 检查配置文件..."
if [ ! -f "server/.env" ]; then
    echo "⚠️  未找到server/.env文件，从示例文件复制..."
    cp server/.env.example server/.env
    echo "✅ 已创建server/.env文件，请根据需要修改配置"
fi

# 创建日志目录
mkdir -p server/logs

echo ""
echo "🎯 启动开发服务器..."
echo "前端地址: http://localhost:5174"
echo "后端地址: http://localhost:3001"
echo "后端API信息: http://localhost:3001/"
echo "健康检查: http://localhost:3001/health"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "================================"

# 启动开发服务器
npm run dev
