# 🔄 企业画像师平台重启指南

## 🚨 关闭所有Node服务并重新启动

### 方法一：强制清理重启（推荐）

**Windows用户：**
```bash
force-clean-start.bat
```

**Linux/Mac用户：**
```bash
chmod +x force-clean-start.sh
./force-clean-start.sh
```

这个脚本会：
- 🔄 强制关闭所有Node.js、npm、tsx进程
- 🔍 释放端口5173和3001
- 🧹 清理npm缓存和日志文件
- 🗑️ 删除并重新安装所有依赖
- 🚀 启动全新的开发环境

### 方法二：常规清理重启

**Windows用户：**
```bash
clean-start.bat
```

**Linux/Mac用户：**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

### 方法三：手动操作

1. **关闭所有Node进程**
```bash
# Windows
taskkill /F /IM node.exe
taskkill /F /IM npm.exe

# Linux/Mac
pkill -f node
pkill -f npm
```

2. **检查端口占用**
```bash
# Windows
netstat -ano | findstr :5173
netstat -ano | findstr :3001

# Linux/Mac
lsof -i:5173
lsof -i:3001
```

3. **强制释放端口**
```bash
# Windows (替换PID为实际进程ID)
taskkill /F /PID <PID>

# Linux/Mac
kill -9 <PID>
```

4. **重新安装依赖**
```bash
# 删除依赖
rm -rf node_modules server/node_modules  # Linux/Mac
rmdir /s /q node_modules server\node_modules  # Windows

# 重新安装
npm install
cd server && npm install && cd ..
```

5. **启动服务**
```bash
npm run dev
```

## 🔍 状态检查

### 快速状态检查
```bash
npm run status
# 或
node check-status.js
```

这会检查：
- ✅ 端口可用性
- ✅ 服务运行状态
- ✅ 文件结构完整性
- ✅ 依赖安装状态

### 手动检查

1. **检查前端**
   - 访问：http://localhost:5173
   - 应该看到"企业画像师"界面

2. **检查后端**
   - 访问：http://localhost:3001/health
   - 应该返回健康状态JSON

3. **检查AI服务**
   - 访问：http://localhost:3001/api/ai/status
   - 应该返回AI服务状态

## 🐛 常见问题解决

### 问题1：端口被占用
```
Error: listen EADDRINUSE: address already in use :::5173
```

**解决方案：**
```bash
# 运行强制清理脚本
force-clean-start.bat  # Windows
./force-clean-start.sh  # Linux/Mac
```

### 问题2：依赖安装失败
```
npm ERR! code ENOENT
```

**解决方案：**
```bash
# 清理npm缓存
npm cache clean --force

# 删除依赖重新安装
npm run clean
npm install
cd server && npm install
```

### 问题3：后端启动失败
```
Error: Cannot find module 'tsx'
```

**解决方案：**
```bash
cd server
npm install tsx --save-dev
npm run dev
```

### 问题4：前端无法连接后端
```
Failed to fetch
```

**解决方案：**
1. 确认后端服务正在运行（端口3001）
2. 检查CORS配置
3. 查看浏览器控制台错误

## 📋 启动后验证清单

### ✅ 前端验证
- [ ] 访问 http://localhost:5173 正常
- [ ] 看到"企业画像师"标题
- [ ] 左侧导航菜单显示正常
- [ ] 右上角系统状态指示器显示连接状态

### ✅ 后端验证
- [ ] 访问 http://localhost:3001/health 返回健康状态
- [ ] 访问 http://localhost:3001/api/ai/status 返回AI状态
- [ ] 访问 http://localhost:3001/api/enterprises 返回企业数据

### ✅ 功能验证
- [ ] 仪表板数据加载正常
- [ ] 系统状态显示"已连接"
- [ ] 控制台无错误信息

## 🆘 紧急情况

如果所有方法都无效：

1. **重启计算机**
2. **检查防火墙和杀毒软件**
3. **重新克隆项目**
4. **检查Node.js版本（需要18+）**

## 📞 获取帮助

如果问题仍然存在：
1. 运行 `npm run status` 获取详细状态
2. 查看 `server/logs/error.log` 错误日志
3. 检查浏览器控制台错误信息

---

**记住：force-clean-start 脚本是最彻底的解决方案！**
