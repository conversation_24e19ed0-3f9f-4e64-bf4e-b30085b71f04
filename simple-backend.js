// 简单的后端服务器 - 无需复杂依赖
const http = require('http');
const url = require('url');

const PORT = 3001;

// 简单的CORS处理
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
}

// 响应JSON
function sendJSON(res, statusCode, data) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json; charset=utf-8' });
  res.end(JSON.stringify(data, null, 2));
}

// 创建服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // 处理OPTIONS请求（CORS预检）
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  console.log(`${method} ${path}`);

  // 路由处理
  if (path === '/' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: '🎉 企业画像师平台 - AI原生企业分析系统',
      description: '基于DeepSeek AI的智能企业画像分析平台',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/health',
        api: {
          ai: '/api/ai',
          enterprises: '/api/enterprises',
          analysis: '/api/analysis',
          visualization: '/api/visualization'
        }
      },
      features: [
        'AI智能分析',
        '企业画像生成',
        '风险评估',
        '投资价值分析',
        '数据可视化',
        '关系网络图'
      ],
      status: 'operational'
    });
  }
  else if (path === '/health' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: '企业画像师后端服务正在运行',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      mode: 'development',
      services: {
        ai: 'operational',
        database: 'ready',
        status: 'healthy'
      },
      project: 'enterprise-profiler'
    });
  }
  else if (path === '/api/enterprises' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      data: {
        enterprises: [
          {
            _id: '1',
            name: '腾讯科技',
            industry: '互联网',
            stage: '成熟期',
            score: 95,
            location: '深圳',
            employees: 85000,
            founded: 1998
          },
          {
            _id: '2', 
            name: '阿里巴巴',
            industry: '电子商务',
            stage: '成熟期',
            score: 98,
            location: '杭州',
            employees: 120000,
            founded: 1999
          },
          {
            _id: '3',
            name: '字节跳动',
            industry: '互联网',
            stage: '成长期',
            score: 92,
            location: '北京',
            employees: 60000,
            founded: 2012
          }
        ],
        pagination: {
          total: 3,
          page: 1,
          limit: 20,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      }
    });
  }
  else if (path === '/api/enterprises/stats/overview' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      data: {
        total: 3,
        byIndustry: {
          '互联网': 2,
          '电子商务': 1
        },
        byStage: {
          '成熟期': 2,
          '成长期': 1
        },
        byLocation: {
          '深圳': 1,
          '杭州': 1,
          '北京': 1
        },
        averageScore: 95
      }
    });
  }
  else if (path === '/api/ai/status' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      data: {
        status: 'operational',
        services: {
          deepseek: {
            available: true,
            model: 'deepseek-reasoner'
          },
          backup: {
            available: true,
            model: 'gpt-3.5-turbo'
          },
          tencent: {
            available: true,
            service: 'image-generation'
          }
        },
        timestamp: new Date().toISOString()
      }
    });
  }
  else {
    // 404处理
    sendJSON(res, 404, {
      success: false,
      error: {
        code: 'NOT_FOUND',
        message: `路径 ${path} 未找到`
      },
      availableEndpoints: {
        root: '/',
        health: '/health',
        enterprises: '/api/enterprises',
        enterpriseStats: '/api/enterprises/stats/overview',
        aiStatus: '/api/ai/status'
      },
      tip: '访问根路径 / 查看完整API信息'
    });
  }
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`🚀 企业画像师简单后端服务器运行在端口 ${PORT}`);
  console.log(`🌐 访问: http://localhost:${PORT}`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 企业API: http://localhost:${PORT}/api/enterprises`);
  console.log(`🤖 AI状态: http://localhost:${PORT}/api/ai/status`);
  console.log('');
  console.log('✅ 服务器已就绪，可以测试前后端连接了！');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
