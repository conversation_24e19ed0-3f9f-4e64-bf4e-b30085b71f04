@echo off
echo 🔍 测试前后端连接状态
echo ================================

echo 📡 测试后端服务器...
curl -s http://localhost:3001/ > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端服务器运行正常
    echo 📋 后端API信息:
    curl -s http://localhost:3001/ | jq . 2>nul || curl -s http://localhost:3001/
) else (
    echo ❌ 后端服务器未运行
    echo 💡 请先启动后端服务器: cd server && npm run dev
)

echo.
echo 🏥 测试健康检查...
curl -s http://localhost:3001/health > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 健康检查通过
    curl -s http://localhost:3001/health | jq .message 2>nul || echo "健康检查API正常"
) else (
    echo ❌ 健康检查失败
)

echo.
echo 🧪 测试企业API...
curl -s http://localhost:3001/api/enterprises > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 企业API正常
) else (
    echo ❌ 企业API异常
)

echo.
echo 🤖 测试AI API...
curl -s http://localhost:3001/api/ai/status > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ AI API正常
) else (
    echo ❌ AI API异常
)

echo.
echo 📊 测试可视化API...
curl -s http://localhost:3001/api/visualization/analytics-summary > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 可视化API正常
) else (
    echo ❌ 可视化API异常
)

echo.
echo ================================
echo 🎯 连接测试完成
echo.
echo 💡 如果有API异常，请检查：
echo 1. 后端服务器是否启动 (npm run dev:server)
echo 2. 数据库服务是否运行 (start-databases.bat)
echo 3. 环境变量配置是否正确 (server/.env)
echo.
pause
