import React, { useState, useEffect } from 'react';
import { Building2, Target, Search, TrendingUp, Award, Users, MapPin, Brain, Database, BarChart3, Settings as SettingsIcon } from 'lucide-react';
import { apiService } from './services/api';
import Dashboard from './components/Dashboard';
import ProfilingSteps from './components/ProfilingSteps';
import EnterpriseAnalysis from './components/EnterpriseAnalysis';
import RoleAnalysis from './components/RoleAnalysis';
import UserPathway from './components/UserPathway';
import AIAnalysis from './components/AIAnalysis';
import DataVisualization from './components/DataVisualization';
import Settings from './components/Settings';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [systemStatus, setSystemStatus] = useState({
    backend: false,
    ai: false,
    database: false
  });

  useEffect(() => {
    checkSystemStatus();
    // Check status every 30 seconds
    const interval = setInterval(checkSystemStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const checkSystemStatus = async () => {
    try {
      // Check backend health
      const healthResponse = await apiService.healthCheck();
      const aiResponse = await apiService.getAIStatus();

      setSystemStatus({
        backend: healthResponse.success,
        ai: aiResponse.success,
        database: healthResponse.success && healthResponse.data?.services?.database === 'connected'
      });
    } catch (error) {
      console.error('Failed to check system status:', error);
      setSystemStatus({ backend: false, ai: false, database: false });
    }
  };

  const navigation = [
    { id: 'dashboard', name: '仪表板', icon: BarChart3 },
    { id: 'profiling', name: '画像绘制', icon: Target },
    { id: 'enterprise', name: '企业分析', icon: Building2 },
    { id: 'roles', name: '角色分析', icon: Users },
    { id: 'pathway', name: '用户路径', icon: MapPin },
    { id: 'ai-analysis', name: 'AI智能分析', icon: Brain },
    { id: 'visualization', name: '数据可视化', icon: TrendingUp },
    { id: 'settings', name: '设置', icon: SettingsIcon },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'profiling':
        return <ProfilingSteps />;
      case 'enterprise':
        return <EnterpriseAnalysis />;
      case 'roles':
        return <RoleAnalysis />;
      case 'pathway':
        return <UserPathway />;
      case 'ai-analysis':
        return <AIAnalysis />;
      case 'visualization':
        return <DataVisualization />;
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="bg-slate-800/80 backdrop-blur-sm border-b border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Brain className="w-8 h-8 text-blue-400" />
                <span className="text-xl font-bold text-white">企业画像师</span>
              </div>
              <div className="hidden md:flex items-center space-x-2 text-sm text-slate-400">
                <Database className="w-4 h-4" />
                <span>MongoDB + Neo4j + PostgreSQL</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${systemStatus.backend ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                  <span className={systemStatus.backend ? 'text-green-400' : 'text-red-400'}>
                    后端{systemStatus.backend ? '已连接' : '未连接'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${systemStatus.ai ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                  <span className={systemStatus.ai ? 'text-green-400' : 'text-red-400'}>
                    AI{systemStatus.ai ? '运行中' : '离线'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${systemStatus.database ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                  <span className={systemStatus.database ? 'text-green-400' : 'text-red-400'}>
                    数据库{systemStatus.database ? '已连接' : '未连接'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-slate-800/50 backdrop-blur-sm border-r border-slate-700/50 min-h-screen">
          <div className="p-4">
            <div className="space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    type="button"
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                      activeTab === item.id
                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                        : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{item.name}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}

export default App;