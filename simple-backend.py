#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的后端服务器 - Python版本
用于测试前后端连接
"""

import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from datetime import datetime

PORT = 3001

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', 'http://localhost:5173')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Access-Control-Allow-Credentials', 'true')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def send_json_response(self, status_code, data):
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        response = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response.encode('utf-8'))

    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        print(f"GET {path}")

        if path == '/':
            self.send_json_response(200, {
                "success": True,
                "message": "🎉 企业画像师平台 - AI原生企业分析系统",
                "description": "基于DeepSeek AI的智能企业画像分析平台",
                "version": "1.0.0",
                "timestamp": datetime.now().isoformat(),
                "endpoints": {
                    "health": "/health",
                    "api": {
                        "ai": "/api/ai",
                        "enterprises": "/api/enterprises",
                        "analysis": "/api/analysis",
                        "visualization": "/api/visualization"
                    }
                },
                "features": [
                    "AI智能分析",
                    "企业画像生成",
                    "风险评估",
                    "投资价值分析",
                    "数据可视化",
                    "关系网络图"
                ],
                "status": "operational"
            })
        
        elif path == '/health':
            self.send_json_response(200, {
                "success": True,
                "message": "企业画像师后端服务正在运行",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0",
                "mode": "development",
                "services": {
                    "ai": "operational",
                    "database": "ready",
                    "status": "healthy"
                },
                "project": "enterprise-profiler"
            })
        
        elif path == '/api/enterprises':
            self.send_json_response(200, {
                "success": True,
                "data": {
                    "enterprises": [
                        {
                            "_id": "1",
                            "name": "腾讯科技",
                            "industry": "互联网",
                            "stage": "成熟期",
                            "score": 95,
                            "location": "深圳",
                            "employees": 85000,
                            "founded": 1998
                        },
                        {
                            "_id": "2",
                            "name": "阿里巴巴",
                            "industry": "电子商务",
                            "stage": "成熟期",
                            "score": 98,
                            "location": "杭州",
                            "employees": 120000,
                            "founded": 1999
                        },
                        {
                            "_id": "3",
                            "name": "字节跳动",
                            "industry": "互联网",
                            "stage": "成长期",
                            "score": 92,
                            "location": "北京",
                            "employees": 60000,
                            "founded": 2012
                        }
                    ],
                    "pagination": {
                        "total": 3,
                        "page": 1,
                        "limit": 20,
                        "totalPages": 1,
                        "hasNext": False,
                        "hasPrev": False
                    }
                }
            })
        
        elif path == '/api/enterprises/stats/overview':
            self.send_json_response(200, {
                "success": True,
                "data": {
                    "total": 3,
                    "byIndustry": {
                        "互联网": 2,
                        "电子商务": 1
                    },
                    "byStage": {
                        "成熟期": 2,
                        "成长期": 1
                    },
                    "byLocation": {
                        "深圳": 1,
                        "杭州": 1,
                        "北京": 1
                    },
                    "averageScore": 95
                }
            })
        
        elif path == '/api/ai/status':
            self.send_json_response(200, {
                "success": True,
                "data": {
                    "status": "operational",
                    "services": {
                        "deepseek": {
                            "available": True,
                            "model": "deepseek-reasoner"
                        },
                        "backup": {
                            "available": True,
                            "model": "gpt-3.5-turbo"
                        },
                        "tencent": {
                            "available": True,
                            "service": "image-generation"
                        }
                    },
                    "timestamp": datetime.now().isoformat()
                }
            })
        
        else:
            self.send_json_response(404, {
                "success": False,
                "error": {
                    "code": "NOT_FOUND",
                    "message": f"路径 {path} 未找到"
                },
                "availableEndpoints": {
                    "root": "/",
                    "health": "/health",
                    "enterprises": "/api/enterprises",
                    "enterpriseStats": "/api/enterprises/stats/overview",
                    "aiStatus": "/api/ai/status"
                },
                "tip": "访问根路径 / 查看完整API信息"
            })

def main():
    try:
        with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
            print(f"🚀 企业画像师简单后端服务器(Python版)运行在端口 {PORT}")
            print(f"🌐 访问: http://localhost:{PORT}")
            print(f"🏥 健康检查: http://localhost:{PORT}/health")
            print(f"📊 企业API: http://localhost:{PORT}/api/enterprises")
            print(f"🤖 AI状态: http://localhost:{PORT}/api/ai/status")
            print("")
            print("✅ 服务器已就绪，可以测试前后端连接了！")
            print("按 Ctrl+C 停止服务器")
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 正在关闭服务器...")
        print("✅ 服务器已关闭")

if __name__ == "__main__":
    main()
