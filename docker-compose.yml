version: '3.8'

services:
  # MongoDB
  mongodb:
    image: mongo:7.0
    container_name: enterprise-profiler-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: enterprise_profiler
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - enterprise-network

  # Neo4j
  neo4j:
    image: neo4j:5.15
    container_name: enterprise-profiler-neo4j
    restart: unless-stopped
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      NEO4J_AUTH: neo4j/password
      NEO4J_PLUGINS: '["apoc"]'
      NEO4J_dbms_security_procedures_unrestricted: apoc.*
      NEO4J_dbms_memory_heap_initial__size: 512m
      NEO4J_dbms_memory_heap_max__size: 2G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - ./docker/neo4j/plugins:/plugins
    networks:
      - enterprise-network

  # PostgreSQL
  postgresql:
    image: postgres:16
    container_name: enterprise-profiler-postgresql
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: enterprise_analytics
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./docker/postgresql/init:/docker-entrypoint-initdb.d
    networks:
      - enterprise-network

  # Redis (for caching)
  redis:
    image: redis:7.2-alpine
    container_name: enterprise-profiler-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass password
    volumes:
      - redis_data:/data
    networks:
      - enterprise-network

  # Backend Server
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: enterprise-profiler-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      PORT: 3001
      MONGODB_URI: ***************************************************************************
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USERNAME: neo4j
      NEO4J_PASSWORD: password
      POSTGRES_HOST: postgresql
      POSTGRES_PORT: 5432
      POSTGRES_DB: enterprise_analytics
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      REDIS_URL: redis://redis:6379
      REDIS_PASSWORD: password
    volumes:
      - ./server:/app
      - /app/node_modules
    depends_on:
      - mongodb
      - neo4j
      - postgresql
      - redis
    networks:
      - enterprise-network
    command: npm start

  # Frontend (for development)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: enterprise-profiler-frontend
    restart: unless-stopped
    ports:
      - "5174:5174"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      VITE_API_URL: http://localhost:3001
    depends_on:
      - backend
    networks:
      - enterprise-network
    command: npm run dev

volumes:
  mongodb_data:
  neo4j_data:
  neo4j_logs:
  postgresql_data:
  redis_data:

networks:
  enterprise-network:
    driver: bridge
