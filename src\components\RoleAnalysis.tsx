import React, { useState } from 'react';
import { Users, User, Crown, Shield, Target, Heart, AlertTriangle, TrendingUp } from 'lucide-react';

const RoleAnalysis = () => {
  const [selectedRole, setSelectedRole] = useState('decision-maker');

  const roles = [
    {
      id: 'user',
      name: '使用者',
      description: '强关联、弱决策',
      icon: User,
      color: 'blue',
      characteristics: ['直接使用产品', '了解产品功能', '提供使用反馈', '影响产品选择'],
      painPoints: ['操作复杂', '功能不够', '性能问题', '学习成本高'],
      goals: ['提高工作效率', '简化操作流程', '获得更好体验', '降低工作难度']
    },
    {
      id: 'influencer',
      name: '影响者',
      description: '中关联、中决策',
      icon: Shield,
      color: 'green',
      characteristics: ['参与决策过程', '评估技术方案', '控制预算执行', '影响最终选择'],
      painPoints: ['成本控制压力', '技术风险担忧', '投资回报不确定', '实施难度大'],
      goals: ['控制成本支出', '降低技术风险', '确保项目成功', '证明投资价值']
    },
    {
      id: 'decision-maker',
      name: '决策者',
      description: '弱关联、强决策',
      icon: Crown,
      color: 'purple',
      characteristics: ['最终决策权', '关注战略价值', '承担最终责任', '资源分配权'],
      painPoints: ['信息不对称', '决策风险大', '资源分配压力', '业绩考核压力'],
      goals: ['实现业务目标', '提升竞争优势', '创造股东价值', '推动企业发展']
    }
  ];

  const decisionLogic = [
    { priority: 1, factor: '管理价值', weight: 40, description: '提升管理效率，优化业务流程' },
    { priority: 2, factor: '业务价值', weight: 35, description: '直接创造业务收益，提升竞争力' },
    { priority: 3, factor: '产品价格', weight: 25, description: '成本控制，投资回报率考量' }
  ];

  const selectedRoleData = roles.find(role => role.id === selectedRole);
  const RoleIcon = selectedRoleData?.icon || User;

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-500',
      green: 'bg-green-500',
      purple: 'bg-purple-500',
    };
    return colors[color as keyof typeof colors] || 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">角色信息分析</h1>
          <p className="text-slate-400">深度分析企业内部三个关键决策角色</p>
        </div>
        <button className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200">
          生成角色报告
        </button>
      </div>

      {/* Role Selection */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {roles.map((role) => {
          const Icon = role.icon;
          return (
            <div
              key={role.id}
              onClick={() => setSelectedRole(role.id)}
              className={`bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border cursor-pointer transition-all duration-200 ${
                selectedRole === role.id 
                  ? 'border-blue-500 bg-blue-500/10' 
                  : 'border-slate-700/50 hover:border-slate-600'
              }`}
            >
              <div className="flex items-center space-x-3 mb-4">
                <div className={`w-12 h-12 rounded-lg ${getColorClasses(role.color)} flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">{role.name}</h3>
                  <p className="text-slate-400 text-sm">{role.description}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Selected Role Details */}
      {selectedRoleData && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center space-x-4 mb-6">
            <div className={`w-16 h-16 rounded-xl ${getColorClasses(selectedRoleData.color)} flex items-center justify-center`}>
              <RoleIcon className="w-8 h-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-white">{selectedRoleData.name}</h2>
              <p className="text-slate-400 mt-1">{selectedRoleData.description}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Characteristics */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
                <Users className="w-5 h-5 text-blue-400" />
                <span>角色特征</span>
              </h3>
              <div className="space-y-2">
                {selectedRoleData.characteristics.map((characteristic, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">{characteristic}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Pain Points */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-red-400" />
                <span>痛点分析</span>
              </h3>
              <div className="space-y-2">
                {selectedRoleData.painPoints.map((painPoint, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">{painPoint}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Goals */}
            <div>
              <h3 className="text-lg font-medium text-white mb-4 flex items-center space-x-2">
                <Target className="w-5 h-5 text-green-400" />
                <span>目标期望</span>
              </h3>
              <div className="space-y-2">
                {selectedRoleData.goals.map((goal, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">{goal}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Decision Logic */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h2 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
          <TrendingUp className="w-6 h-6 text-purple-400" />
          <span>决策者选型逻辑</span>
        </h2>
        
        <div className="space-y-4">
          {decisionLogic.map((item, index) => (
            <div key={index} className="flex items-center space-between">
              <div className="flex items-center space-x-4 flex-1">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {item.priority}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white font-medium">{item.factor}</span>
                    <span className="text-slate-400 text-sm">{item.weight}%</span>
                  </div>
                  <p className="text-slate-400 text-sm">{item.description}</p>
                </div>
              </div>
              <div className="w-48 bg-slate-700 rounded-full h-2 ml-4">
                <div 
                  className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${item.weight}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Role Interaction Matrix */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h2 className="text-xl font-semibold text-white mb-6">角色互动关系</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-slate-700/50 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">使用者 → 影响者</h3>
            <p className="text-slate-400 text-sm">提供使用反馈，影响技术评估</p>
          </div>
          
          <div className="bg-slate-700/50 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">影响者 → 决策者</h3>
            <p className="text-slate-400 text-sm">提供专业建议，支撑决策判断</p>
          </div>
          
          <div className="bg-slate-700/50 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">决策者 → 使用者</h3>
            <p className="text-slate-400 text-sm">关注使用效果，验证投资价值</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleAnalysis;