{"name": "enterprise-profiler-server", "version": "1.0.0", "description": "AI-native enterprise profiling backend service", "main": "dist/app.js", "type": "module", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc", "start": "node dist/app.js", "lint": "eslint src/**/*.ts", "test": "jest", "db:setup": "tsx src/scripts/setupDatabases.ts", "db:seed": "tsx src/scripts/seedDatabase.ts", "db:init": "npm run db:setup"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "mongoose": "^8.0.3", "neo4j-driver": "^5.15.0", "pg": "^8.11.3", "joi": "^17.11.0", "winston": "^3.11.0", "axios": "^1.6.2", "openai": "^4.20.1", "tencentcloud-sdk-nodejs": "^4.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.9", "@types/node": "^20.10.5", "typescript": "^5.3.3", "tsx": "^4.6.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["enterprise", "profiling", "ai", "deepseek", "mongodb", "neo4j", "postgresql"], "author": "Enterprise Profiler Team", "license": "MIT"}