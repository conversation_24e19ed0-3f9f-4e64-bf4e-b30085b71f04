import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  RadialLinearScale,
  Filler
} from 'chart.js';
import { <PERSON>, Doughnut, Line, Radar } from 'react-chartjs-2';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  RadialLinearScale,
  Filler
);

// 通用图表配置
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      labels: {
        color: '#e2e8f0', // slate-200
        font: {
          size: 12
        }
      }
    },
    tooltip: {
      backgroundColor: '#1e293b', // slate-800
      titleColor: '#f1f5f9', // slate-100
      bodyColor: '#e2e8f0', // slate-200
      borderColor: '#475569', // slate-600
      borderWidth: 1
    }
  },
  scales: {
    x: {
      ticks: {
        color: '#94a3b8' // slate-400
      },
      grid: {
        color: '#334155' // slate-700
      }
    },
    y: {
      ticks: {
        color: '#94a3b8' // slate-400
      },
      grid: {
        color: '#334155' // slate-700
      }
    }
  }
};

// 企业分布柱状图
export const EnterpriseDistributionChart: React.FC<{
  data: { labels: string[]; datasets: any[] };
  title?: string;
}> = ({ data, title = "企业分布" }) => {
  const options = {
    ...chartOptions,
    plugins: {
      ...chartOptions.plugins,
      title: {
        display: true,
        text: title,
        color: '#f1f5f9',
        font: {
          size: 16,
          weight: 'bold' as const
        }
      }
    }
  };

  return (
    <div className="h-80 w-full">
      <Bar data={data} options={options} />
    </div>
  );
};

// 行业分布饼图
export const IndustryPieChart: React.FC<{
  data: { labels: string[]; datasets: any[] };
  title?: string;
}> = ({ data, title = "行业分布" }) => {
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          color: '#e2e8f0',
          font: {
            size: 12
          },
          padding: 20
        }
      },
      title: {
        display: true,
        text: title,
        color: '#f1f5f9',
        font: {
          size: 16,
          weight: 'bold' as const
        }
      },
      tooltip: {
        backgroundColor: '#1e293b',
        titleColor: '#f1f5f9',
        bodyColor: '#e2e8f0',
        borderColor: '#475569',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed || 0;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    }
  };

  return (
    <div className="h-80 w-full">
      <Doughnut data={data} options={options} />
    </div>
  );
};

// 趋势线图
export const TrendLineChart: React.FC<{
  data: { labels: string[]; datasets: any[] };
  title?: string;
}> = ({ data, title = "趋势分析" }) => {
  const options = {
    ...chartOptions,
    plugins: {
      ...chartOptions.plugins,
      title: {
        display: true,
        text: title,
        color: '#f1f5f9',
        font: {
          size: 16,
          weight: 'bold' as const
        }
      }
    },
    elements: {
      line: {
        tension: 0.4
      },
      point: {
        radius: 4,
        hoverRadius: 6
      }
    }
  };

  return (
    <div className="h-80 w-full">
      <Line data={data} options={options} />
    </div>
  );
};

// 雷达图（用于企业评分）
export const EnterpriseRadarChart: React.FC<{
  data: { labels: string[]; datasets: any[] };
  title?: string;
}> = ({ data, title = "企业评分雷达图" }) => {
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: '#e2e8f0',
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: title,
        color: '#f1f5f9',
        font: {
          size: 16,
          weight: 'bold' as const
        }
      },
      tooltip: {
        backgroundColor: '#1e293b',
        titleColor: '#f1f5f9',
        bodyColor: '#e2e8f0',
        borderColor: '#475569',
        borderWidth: 1
      }
    },
    scales: {
      r: {
        angleLines: {
          color: '#334155'
        },
        grid: {
          color: '#334155'
        },
        pointLabels: {
          color: '#94a3b8',
          font: {
            size: 11
          }
        },
        ticks: {
          color: '#94a3b8',
          backdropColor: 'transparent'
        },
        min: 0,
        max: 100
      }
    },
    elements: {
      line: {
        borderWidth: 2
      },
      point: {
        radius: 3,
        hoverRadius: 5
      }
    }
  };

  return (
    <div className="h-80 w-full">
      <Radar data={data} options={options} />
    </div>
  );
};

// 数据处理工具函数
export const chartUtils = {
  // 生成企业分布数据
  generateEnterpriseDistribution: (enterprises: any[]) => {
    const distribution = enterprises.reduce((acc, enterprise) => {
      const stage = enterprise.stage || '未知';
      acc[stage] = (acc[stage] || 0) + 1;
      return acc;
    }, {});

    return {
      labels: Object.keys(distribution),
      datasets: [{
        label: '企业数量',
        data: Object.values(distribution),
        backgroundColor: [
          '#3b82f6', // blue-500
          '#10b981', // emerald-500
          '#f59e0b', // amber-500
          '#ef4444', // red-500
          '#8b5cf6', // violet-500
        ],
        borderColor: [
          '#2563eb', // blue-600
          '#059669', // emerald-600
          '#d97706', // amber-600
          '#dc2626', // red-600
          '#7c3aed', // violet-600
        ],
        borderWidth: 1
      }]
    };
  },

  // 生成行业分布数据
  generateIndustryDistribution: (enterprises: any[]) => {
    const distribution = enterprises.reduce((acc, enterprise) => {
      const industry = enterprise.industry || '未知';
      acc[industry] = (acc[industry] || 0) + 1;
      return acc;
    }, {});

    return {
      labels: Object.keys(distribution),
      datasets: [{
        data: Object.values(distribution),
        backgroundColor: [
          '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
          '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
        ],
        borderColor: '#1e293b',
        borderWidth: 2
      }]
    };
  },

  // 生成趋势数据
  generateTrendData: (data: any[], dateField: string, valueField: string) => {
    const sortedData = data.sort((a, b) => 
      new Date(a[dateField]).getTime() - new Date(b[dateField]).getTime()
    );

    const labels = sortedData.map(item => 
      new Date(item[dateField]).toLocaleDateString()
    );
    const values = sortedData.map(item => item[valueField]);

    return {
      labels,
      datasets: [{
        label: '趋势',
        data: values,
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4
      }]
    };
  },

  // 生成雷达图数据
  generateRadarData: (enterprise: any) => {
    const dimensions = [
      { name: '业务成熟度', value: enterprise.businessMaturity || 0 },
      { name: '成长潜力', value: enterprise.growthPotential || 0 },
      { name: '财务健康', value: enterprise.financialHealth || 0 },
      { name: '技术创新', value: enterprise.techInnovation || 0 },
      { name: '市场地位', value: enterprise.marketPosition || 0 },
      { name: '风险控制', value: enterprise.riskControl || 0 }
    ];

    return {
      labels: dimensions.map(d => d.name),
      datasets: [{
        label: enterprise.name || '企业评分',
        data: dimensions.map(d => d.value),
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        pointBackgroundColor: '#3b82f6',
        pointBorderColor: '#1e40af',
        pointHoverBackgroundColor: '#1e40af',
        pointHoverBorderColor: '#3b82f6'
      }]
    };
  }
};
