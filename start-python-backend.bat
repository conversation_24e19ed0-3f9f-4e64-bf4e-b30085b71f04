@echo off
echo 🐍 启动Python后端服务器
echo ================================

echo 🔍 检查Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装，尝试python3...
    python3 --version
    if %errorlevel% neq 0 (
        echo ❌ Python未安装或不在PATH中
        echo 💡 请安装Python 3.6+
        pause
        exit /b 1
    ) else (
        echo ✅ 使用python3启动
        python3 simple-backend.py
    )
) else (
    echo ✅ 使用python启动
    python simple-backend.py
)

pause
