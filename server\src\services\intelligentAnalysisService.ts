import { AIService } from './aiService.js';
import { enterpriseService } from './enterpriseService.js';
import { neo4jService } from './neo4jService.js';
import { postgresService } from './postgresService.js';
import { Enterprise, type IEnterprise } from '../models/Enterprise.js';
import { AnalysisResult } from '../models/AnalysisResult.js';
import { aiLogger } from '../utils/logger.js';
import { CustomError } from '../middleware/errorHandler.js';

// Create AI service instance
const aiService = new AIService();

export interface IntelligentAnalysisRequest {
  enterpriseId: string;
  analysisTypes: ('profile' | 'risk' | 'investment' | 'competitive' | 'pathway')[];
  includeCompetitors?: boolean;
  depth?: 'basic' | 'standard' | 'comprehensive';
}

export interface AnalysisResult {
  enterpriseId: string;
  analysisType: string;
  result: any;
  confidence: number;
  processingTime: number;
  recommendations: string[];
}

export class IntelligentAnalysisService {
  /**
   * 执行智能企业分析
   */
  async performIntelligentAnalysis(request: IntelligentAnalysisRequest): Promise<{
    results: AnalysisResult[];
    summary: any;
    totalProcessingTime: number;
  }> {
    const startTime = Date.now();
    const results: AnalysisResult[] = [];

    try {
      aiLogger.info('Starting intelligent analysis', { 
        enterpriseId: request.enterpriseId,
        analysisTypes: request.analysisTypes
      });

      // 获取企业基础信息
      const enterprise = await enterpriseService.getEnterpriseById(request.enterpriseId);
      if (!enterprise) {
        throw new CustomError('企业不存在', 404);
      }

      // 获取竞争对手信息（如果需要）
      let competitors: any[] = [];
      if (request.includeCompetitors) {
        competitors = await neo4jService.findSimilarEnterprises(request.enterpriseId, 5);
      }

      // 执行各类分析
      for (const analysisType of request.analysisTypes) {
        const analysisResult = await this.performSingleAnalysis(
          enterprise,
          analysisType,
          competitors,
          request.depth || 'standard'
        );
        results.push(analysisResult);

        // 保存分析结果到数据库
        await this.saveAnalysisResult(analysisResult);
      }

      // 生成综合分析摘要
      const summary = await this.generateAnalysisSummary(enterprise, results);

      const totalProcessingTime = Date.now() - startTime;

      aiLogger.info('Intelligent analysis completed', {
        enterpriseId: request.enterpriseId,
        resultsCount: results.length,
        totalProcessingTime
      });

      return {
        results,
        summary,
        totalProcessingTime
      };

    } catch (error) {
      aiLogger.error('Intelligent analysis failed:', error);
      throw error;
    }
  }

  /**
   * 执行单项分析
   */
  private async performSingleAnalysis(
    enterprise: IEnterprise,
    analysisType: string,
    competitors: IEnterprise[],
    depth: string
  ): Promise<AnalysisResult> {
    const startTime = Date.now();

    try {
      let aiResponse;
      let analysisResult;

      switch (analysisType) {
        case 'profile':
          aiResponse = await aiService.analyzeEnterprise({
            companyName: enterprise.name,
            industry: enterprise.industry,
            description: enterprise.description || ''
          });
          break;

        case 'risk':
          aiResponse = await aiService.assessRisks(enterprise.toObject());
          break;

        case 'investment':
          aiResponse = await aiService.evaluateInvestmentValue(
            enterprise.toObject(),
            { revenue: enterprise.revenue, employees: enterprise.employees }
          );
          break;

        case 'competitive':
          aiResponse = await aiService.deepAnalyzeEnterprise(
            enterprise.toObject(),
            competitors.map(c => c.toObject())
          );
          break;

        case 'pathway':
          const roleData = await this.generateRoleData(enterprise);
          aiResponse = await aiService.analyzeUserPathway(
            enterprise.toObject(),
            roleData
          );
          break;

        default:
          throw new CustomError(`不支持的分析类型: ${analysisType}`, 400);
      }

      if (!aiResponse.success) {
        throw new CustomError(`AI分析失败: ${aiResponse.error}`, 500);
      }

      // 解析AI响应
      let parsedResult;
      try {
        parsedResult = typeof aiResponse.data === 'string' 
          ? JSON.parse(aiResponse.data) 
          : aiResponse.data;
      } catch (parseError) {
        aiLogger.warn('Failed to parse AI response as JSON, using raw response');
        parsedResult = { rawResponse: aiResponse.data };
      }

      const processingTime = Date.now() - startTime;

      analysisResult = {
        enterpriseId: (enterprise._id as any).toString(),
        analysisType,
        result: parsedResult,
        confidence: parsedResult.confidence || 0.8,
        processingTime,
        recommendations: parsedResult.recommendations || []
      };

      // 更新企业分析数据
      await this.updateEnterpriseAnalysisData(enterprise, analysisType, parsedResult);

      return analysisResult;

    } catch (error) {
      aiLogger.error(`Analysis failed for type ${analysisType}:`, error);
      throw error;
    }
  }

  /**
   * 生成角色数据
   */
  private async generateRoleData(enterprise: IEnterprise): Promise<any> {
    return {
      decisionMaker: {
        title: enterprise.size && parseInt(enterprise.size.split('-')[0]) > 100 ? 'CEO' : 'Founder',
        influence: enterprise.stage === '成熟期' ? 90 : 95,
        characteristics: ['技术导向', '注重效率']
      },
      influencer: {
        title: '技术负责人',
        influence: 80,
        characteristics: ['专业性强', '关注技术细节']
      },
      user: {
        title: '开发团队',
        influence: 60,
        characteristics: ['关注易用性', '重视稳定性']
      }
    };
  }

  /**
   * 更新企业分析数据
   */
  private async updateEnterpriseAnalysisData(
    enterprise: IEnterprise,
    analysisType: string,
    result: any
  ): Promise<void> {
    try {
      const updateData: any = {
        'analysisData.lastAnalyzed': new Date()
      };

      if (result.overallScore) {
        updateData.score = result.overallScore;
      }

      if (result.riskLevel) {
        updateData['analysisData.riskLevel'] = result.riskLevel;
      }

      if (result.insights) {
        updateData['analysisData.aiInsights'] = {
          ...enterprise.analysisData?.aiInsights,
          [analysisType]: result
        };
      }

      await enterpriseService.updateEnterprise((enterprise._id as any).toString(), updateData);

    } catch (error) {
      aiLogger.error('Failed to update enterprise analysis data:', error);
    }
  }

  /**
   * 保存分析结果
   */
  private async saveAnalysisResult(analysisResult: AnalysisResult): Promise<void> {
    try {
      await postgresService.saveAnalyticsResult({
        enterprise_id: analysisResult.enterpriseId,
        analysis_type: analysisResult.analysisType,
        result_data: analysisResult.result,
        confidence_score: analysisResult.confidence
      });

      // 记录用户活动
      await postgresService.logUserActivity({
        user_id: 'system',
        action: 'ai_analysis',
        resource_type: 'enterprise',
        resource_id: analysisResult.enterpriseId,
        metadata: {
          analysis_type: analysisResult.analysisType,
          confidence: analysisResult.confidence,
          processing_time: analysisResult.processingTime
        }
      });

    } catch (error) {
      aiLogger.error('Failed to save analysis result:', error);
    }
  }

  /**
   * 生成分析摘要
   */
  private async generateAnalysisSummary(
    enterprise: IEnterprise,
    results: AnalysisResult[]
  ): Promise<any> {
    const avgConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;
    const totalProcessingTime = results.reduce((sum, r) => sum + r.processingTime, 0);

    const summary = {
      enterprise: {
        id: (enterprise._id as any).toString(),
        name: enterprise.name,
        industry: enterprise.industry,
        stage: enterprise.stage
      },
      analysis: {
        types: results.map(r => r.analysisType),
        averageConfidence: avgConfidence,
        totalProcessingTime,
        completedAt: new Date()
      },
      keyInsights: this.extractKeyInsights(results),
      overallRecommendations: this.generateOverallRecommendations(results),
      nextSteps: this.generateNextSteps(results)
    };

    return summary;
  }

  /**
   * 提取关键洞察
   */
  private extractKeyInsights(results: AnalysisResult[]): string[] {
    const insights: string[] = [];

    results.forEach(result => {
      if (result.result.insights) {
        insights.push(...result.result.insights);
      }
      if (result.result.overallAssessment) {
        insights.push(result.result.overallAssessment);
      }
    });

    return insights.slice(0, 5); // 返回前5个关键洞察
  }

  /**
   * 生成综合建议
   */
  private generateOverallRecommendations(results: AnalysisResult[]): string[] {
    const recommendations: string[] = [];

    results.forEach(result => {
      if (result.recommendations) {
        recommendations.push(...result.recommendations);
      }
      if (result.result.recommendations) {
        if (Array.isArray(result.result.recommendations)) {
          recommendations.push(...result.result.recommendations.map((r: any) => r.suggestion || r));
        }
      }
    });

    return [...new Set(recommendations)].slice(0, 8); // 去重并返回前8个建议
  }

  /**
   * 生成下一步行动
   */
  private generateNextSteps(results: AnalysisResult[]): string[] {
    const nextSteps: string[] = [];

    results.forEach(result => {
      if (result.result.nextSteps) {
        nextSteps.push(...result.result.nextSteps);
      }
    });

    return [...new Set(nextSteps)].slice(0, 5); // 去重并返回前5个行动项
  }

  /**
   * 获取企业历史分析结果
   */
  async getEnterpriseAnalysisHistory(
    enterpriseId: string,
    analysisType?: string
  ): Promise<any[]> {
    try {
      return await postgresService.getAnalyticsResults(enterpriseId, analysisType);
    } catch (error) {
      aiLogger.error('Failed to get analysis history:', error);
      throw error;
    }
  }
}

export const intelligentAnalysisService = new IntelligentAnalysisService();
