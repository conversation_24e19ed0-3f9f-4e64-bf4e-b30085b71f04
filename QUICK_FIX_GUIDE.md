# 🚀 快速修复指南 - 解决3001端口问题

## 🎯 问题描述
访问 http://localhost:3001 返回 `{"success":false,"error":{"code":"NOT_FOUND","message":"路径 / 未找到"}}`

## ✅ 立即解决方案

### 方案1：使用Node.js简单服务器（推荐）

1. **打开命令提示符**（以管理员身份运行）

2. **导航到项目目录**：
```cmd
cd /d E:\gitHub-b\Enterprise-Profiler-0707-B
```

3. **启动简单后端**：
```cmd
node simple-backend.js
```

4. **验证服务器运行**：
   - 浏览器访问：http://localhost:3001
   - 应该看到完整的API信息JSON响应

### 方案2：使用Python服务器（备用）

如果Node.js有问题，使用Python：

1. **检查Python安装**：
```cmd
python --version
```

2. **启动Python服务器**：
```cmd
python simple-backend.py
```

3. **或使用批处理脚本**：
```cmd
start-python-backend.bat
```

### 方案3：使用批处理脚本

直接双击以下文件之一：
- `start-simple-backend.bat` (Node.js版本)
- `start-python-backend.bat` (Python版本)

## 🔍 验证步骤

### 1. 后端API测试
访问以下URL，应该都返回JSON响应：

- **根路径**: http://localhost:3001/
  ```json
  {
    "success": true,
    "message": "🎉 企业画像师平台 - AI原生企业分析系统",
    ...
  }
  ```

- **健康检查**: http://localhost:3001/health
  ```json
  {
    "success": true,
    "message": "企业画像师后端服务正在运行",
    ...
  }
  ```

- **企业API**: http://localhost:3001/api/enterprises
  ```json
  {
    "success": true,
    "data": {
      "enterprises": [...]
    }
  }
  ```

### 2. 前端连接测试

1. **启动前端**（在新的命令提示符窗口）：
```cmd
cd /d E:\gitHub-b\Enterprise-Profiler-0707-B
npm run dev
```

2. **访问前端**：http://localhost:5173

3. **检查前端控制台**：
   - 按F12打开开发者工具
   - 查看Console标签，不应该有API连接错误

## 🛠️ 故障排除

### 问题1：Node.js命令不识别
**解决方案**：
1. 从 https://nodejs.org 下载并安装Node.js
2. 重启命令提示符
3. 验证：`node --version`

### 问题2：端口被占用
**解决方案**：
```cmd
# 查看端口占用
netstat -ano | findstr :3001

# 结束占用进程（替换PID）
taskkill /F /PID <PID>
```

### 问题3：CORS错误
**解决方案**：
- 确保后端服务器正在运行
- 检查前端访问地址是 http://localhost:5173
- 简单服务器已配置正确的CORS头

### 问题4：前端API配置错误
**检查文件**：`.env`
```
VITE_API_URL=http://localhost:3001
```

**检查文件**：`src/services/api.ts`
```typescript
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';
```

## 📋 完整启动流程

### 步骤1：启动后端
```cmd
# 方法1：Node.js
cd /d E:\gitHub-b\Enterprise-Profiler-0707-B
node simple-backend.js

# 方法2：Python
python simple-backend.py

# 方法3：批处理
start-simple-backend.bat
```

### 步骤2：验证后端
浏览器访问：http://localhost:3001

### 步骤3：启动前端
```cmd
# 新的命令提示符窗口
cd /d E:\gitHub-b\Enterprise-Profiler-0707-B
npm run dev
```

### 步骤4：验证前端
浏览器访问：http://localhost:5173

## 🎯 预期结果

### 后端正常运行时
- ✅ http://localhost:3001/ 返回API信息
- ✅ http://localhost:3001/health 返回健康状态
- ✅ http://localhost:3001/api/enterprises 返回企业数据

### 前端正常连接时
- ✅ 前端页面正常加载
- ✅ 浏览器控制台无API错误
- ✅ 企业数据正常显示

## 🆘 如果仍有问题

1. **检查防火墙设置**：确保3001和5173端口未被阻止

2. **检查杀毒软件**：某些杀毒软件可能阻止本地服务器

3. **重启计算机**：清除所有进程和网络状态

4. **使用不同端口**：
   - 修改简单服务器中的PORT变量
   - 相应修改前端配置

## 💡 提示

- 简单服务器提供了基本的API模拟，足以测试前后端连接
- 一旦连接正常，可以逐步迁移到完整的后端服务
- 保持两个命令提示符窗口：一个运行后端，一个运行前端

---

**🎉 按照这个指南，您的前后端连接问题应该能够快速解决！**
