import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON>, TrendingUp, Network, RefreshCw, Download, Filter } from 'lucide-react';
import { apiService } from '../services/api';
import {
  EnterpriseDistributionChart,
  IndustryPieChart,
  TrendLineChart,
  EnterpriseRadarChart,
  chartUtils
} from './charts/ChartComponents';
import NetworkGraph from './charts/NetworkGraph';

const DataVisualization = () => {
  const [enterprises, setEnterprises] = useState([]);
  const [analysisData, setAnalysisData] = useState([]);
  const [networkData, setNetworkData] = useState({ nodes: [], links: [] });
  const [selectedView, setSelectedView] = useState('overview');
  const [selectedEnterprise, setSelectedEnterprise] = useState(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    industry: '',
    stage: '',
    minScore: 0,
    maxScore: 100
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // 加载企业数据
      const enterpriseResponse = await apiService.getEnterprises({ limit: 100 });
      if (enterpriseResponse.success) {
        setEnterprises(enterpriseResponse.data.enterprises);
      }

      // 加载分析数据（模拟）
      const mockAnalysisData = generateMockAnalysisData();
      setAnalysisData(mockAnalysisData);

      // 生成网络图数据
      if (enterpriseResponse.success) {
        const networkData = generateNetworkData(enterpriseResponse.data.enterprises);
        setNetworkData(networkData);
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateMockAnalysisData = () => {
    const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
    return months.map((month, index) => ({
      month,
      enterprises: 20 + index * 5,
      analyses: 15 + index * 3,
      score: 75 + Math.random() * 20
    }));
  };

  const generateNetworkData = (enterprises: any[]) => {
    const nodes: any[] = [];
    const links: any[] = [];

    // 添加企业节点
    enterprises.slice(0, 20).forEach(enterprise => {
      nodes.push({
        id: enterprise._id,
        name: enterprise.name,
        type: 'enterprise',
        industry: enterprise.industry,
        location: enterprise.location,
        score: enterprise.score || 75,
        size: Math.max(15, (enterprise.score || 75) / 5)
      });
    });

    // 添加行业节点
    const industries = [...new Set(enterprises.map(e => e.industry))];
    industries.forEach(industry => {
      nodes.push({
        id: `industry_${industry}`,
        name: industry,
        type: 'industry',
        size: 25
      });
    });

    // 添加地区节点
    const locations = [...new Set(enterprises.map(e => e.location).filter(Boolean))];
    locations.forEach(location => {
      nodes.push({
        id: `location_${location}`,
        name: location,
        type: 'location',
        size: 20
      });
    });

    // 添加企业到行业的连接
    enterprises.slice(0, 20).forEach(enterprise => {
      links.push({
        source: enterprise._id,
        target: `industry_${enterprise.industry}`,
        type: 'BELONGS_TO',
        strength: 0.8
      });

      if (enterprise.location) {
        links.push({
          source: enterprise._id,
          target: `location_${enterprise.location}`,
          type: 'LOCATED_IN',
          strength: 0.6
        });
      }
    });

    // 添加一些企业间的竞争关系
    for (let i = 0; i < enterprises.length - 1 && i < 15; i++) {
      const current = enterprises[i];
      const next = enterprises[i + 1];
      if (current.industry === next.industry) {
        links.push({
          source: current._id,
          target: next._id,
          type: 'COMPETES_WITH',
          strength: 0.3
        });
      }
    }

    return { nodes, links };
  };

  const filteredEnterprises = enterprises.filter(enterprise => {
    return (
      (!filters.industry || (enterprise as any).industry.includes(filters.industry)) &&
      (!filters.stage || (enterprise as any).stage === filters.stage) &&
      ((enterprise as any).score || 0) >= filters.minScore &&
      ((enterprise as any).score || 0) <= filters.maxScore
    );
  });

  const handleNodeClick = (node: any) => {
    if (node.type === 'enterprise') {
      const enterprise = enterprises.find((e: any) => e._id === node.id);
      setSelectedEnterprise(enterprise);
    }
  };

  const exportData = () => {
    const dataToExport = {
      enterprises: filteredEnterprises,
      analysisData,
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `enterprise-data-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
        <span className="ml-2 text-slate-400">加载数据中...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和控制 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">数据可视化</h1>
          <p className="text-slate-400 mt-1">企业数据的交互式可视化分析</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={loadData}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-white transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>刷新数据</span>
          </button>
          <button
            onClick={exportData}
            className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg text-white transition-colors"
          >
            <Download className="w-4 h-4" />
            <span>导出数据</span>
          </button>
        </div>
      </div>

      {/* 视图切换 */}
      <div className="flex space-x-2 bg-slate-800 p-1 rounded-lg">
        {[
          { id: 'overview', name: '概览', icon: BarChart3 },
          { id: 'distribution', name: '分布分析', icon: PieChart },
          { id: 'trends', name: '趋势分析', icon: TrendingUp },
          { id: 'network', name: '关系网络', icon: Network }
        ].map(view => {
          const Icon = view.icon;
          return (
            <button
              key={view.id}
              onClick={() => setSelectedView(view.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                selectedView === view.id
                  ? 'bg-blue-600 text-white'
                  : 'text-slate-400 hover:text-white hover:bg-slate-700'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{view.name}</span>
            </button>
          );
        })}
      </div>

      {/* 筛选器 */}
      <div className="bg-slate-800 rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <Filter className="w-5 h-5 text-slate-400" />
          <div className="flex items-center space-x-4 flex-1">
            <div>
              <label className="block text-sm text-slate-400 mb-1">行业</label>
              <input
                type="text"
                value={filters.industry}
                onChange={(e) => setFilters(prev => ({ ...prev, industry: e.target.value }))}
                placeholder="筛选行业"
                className="bg-slate-700 border border-slate-600 rounded px-3 py-1 text-white text-sm"
              />
            </div>
            <div>
              <label className="block text-sm text-slate-400 mb-1">发展阶段</label>
              <select
                value={filters.stage}
                onChange={(e) => setFilters(prev => ({ ...prev, stage: e.target.value }))}
                className="bg-slate-700 border border-slate-600 rounded px-3 py-1 text-white text-sm"
              >
                <option value="">全部</option>
                <option value="初创期">初创期</option>
                <option value="成长期">成长期</option>
                <option value="成熟期">成熟期</option>
              </select>
            </div>
            <div>
              <label className="block text-sm text-slate-400 mb-1">评分范围</label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={filters.minScore}
                  onChange={(e) => setFilters(prev => ({ ...prev, minScore: Number(e.target.value) }))}
                  min="0"
                  max="100"
                  className="bg-slate-700 border border-slate-600 rounded px-2 py-1 text-white text-sm w-16"
                />
                <span className="text-slate-400">-</span>
                <input
                  type="number"
                  value={filters.maxScore}
                  onChange={(e) => setFilters(prev => ({ ...prev, maxScore: Number(e.target.value) }))}
                  min="0"
                  max="100"
                  className="bg-slate-700 border border-slate-600 rounded px-2 py-1 text-white text-sm w-16"
                />
              </div>
            </div>
          </div>
          <div className="text-sm text-slate-400">
            显示 {filteredEnterprises.length} / {enterprises.length} 个企业
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧图表区域 */}
        <div className="lg:col-span-2 space-y-6">
          {selectedView === 'overview' && (
            <>
              <div className="bg-slate-800 rounded-lg p-6">
                <EnterpriseDistributionChart
                  data={chartUtils.generateEnterpriseDistribution(filteredEnterprises)}
                  title="企业发展阶段分布"
                />
              </div>
              <div className="bg-slate-800 rounded-lg p-6">
                <TrendLineChart
                  data={chartUtils.generateTrendData(analysisData, 'month', 'enterprises')}
                  title="企业数量增长趋势"
                />
              </div>
            </>
          )}

          {selectedView === 'distribution' && (
            <>
              <div className="bg-slate-800 rounded-lg p-6">
                <IndustryPieChart
                  data={chartUtils.generateIndustryDistribution(filteredEnterprises)}
                  title="行业分布"
                />
              </div>
              <div className="bg-slate-800 rounded-lg p-6">
                <EnterpriseDistributionChart
                  data={chartUtils.generateEnterpriseDistribution(filteredEnterprises)}
                  title="发展阶段分布"
                />
              </div>
            </>
          )}

          {selectedView === 'trends' && (
            <div className="space-y-6">
              <div className="bg-slate-800 rounded-lg p-6">
                <TrendLineChart
                  data={chartUtils.generateTrendData(analysisData, 'month', 'enterprises')}
                  title="企业数量趋势"
                />
              </div>
              <div className="bg-slate-800 rounded-lg p-6">
                <TrendLineChart
                  data={chartUtils.generateTrendData(analysisData, 'month', 'score')}
                  title="平均评分趋势"
                />
              </div>
            </div>
          )}

          {selectedView === 'network' && (
            <div className="bg-slate-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">企业关系网络</h3>
              <NetworkGraph
                nodes={networkData.nodes}
                links={networkData.links}
                width={600}
                height={500}
                onNodeClick={handleNodeClick}
              />
            </div>
          )}
        </div>

        {/* 右侧信息面板 */}
        <div className="space-y-6">
          {/* 统计卡片 */}
          <div className="bg-slate-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">数据统计</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-slate-400">企业总数</span>
                <span className="text-white font-medium">{enterprises.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">筛选结果</span>
                <span className="text-white font-medium">{filteredEnterprises.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">平均评分</span>
                <span className="text-white font-medium">
                  {(filteredEnterprises.reduce((sum, e) => sum + ((e as any).score || 0), 0) / filteredEnterprises.length || 0).toFixed(1)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">行业数量</span>
                <span className="text-white font-medium">
                  {new Set(filteredEnterprises.map((e: any) => e.industry)).size}
                </span>
              </div>
            </div>
          </div>

          {/* 选中企业详情 */}
          {selectedEnterprise && (
            <div className="bg-slate-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">企业详情</h3>
              <div className="space-y-3">
                <div>
                  <span className="text-slate-400">企业名称</span>
                  <div className="text-white font-medium">{(selectedEnterprise as any).name}</div>
                </div>
                <div>
                  <span className="text-slate-400">所属行业</span>
                  <div className="text-white">{(selectedEnterprise as any).industry}</div>
                </div>
                <div>
                  <span className="text-slate-400">发展阶段</span>
                  <div className="text-white">{(selectedEnterprise as any).stage}</div>
                </div>
                <div>
                  <span className="text-slate-400">综合评分</span>
                  <div className="text-white font-medium">{(selectedEnterprise as any).score || 'N/A'}</div>
                </div>
              </div>
              
              {/* 企业雷达图 */}
              <div className="mt-6">
                <EnterpriseRadarChart
                  data={chartUtils.generateRadarData(selectedEnterprise)}
                  title="企业能力雷达图"
                />
              </div>
            </div>
          )}

          {/* 快速操作 */}
          <div className="bg-slate-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">快速操作</h3>
            <div className="space-y-2">
              <button className="w-full text-left px-3 py-2 text-slate-300 hover:text-white hover:bg-slate-700 rounded transition-colors">
                查看详细报告
              </button>
              <button className="w-full text-left px-3 py-2 text-slate-300 hover:text-white hover:bg-slate-700 rounded transition-colors">
                导出图表
              </button>
              <button className="w-full text-left px-3 py-2 text-slate-300 hover:text-white hover:bg-slate-700 rounded transition-colors">
                设置提醒
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataVisualization;
