import neo4j, { Driver, Session, Result } from 'neo4j-driver';
import { getNeo4jDriver } from '../config/database.js';
import { dbLogger } from '../utils/logger.js';
import { CustomError } from '../middleware/errorHandler.js';

export interface EnterpriseNode {
  id: string;
  name: string;
  industry: string;
  location: string;
  stage: string;
  size: string;
  score: number;
}

export interface RelationshipData {
  type: string;
  properties?: Record<string, any>;
}

export interface GraphAnalysisResult {
  nodes: Array<{
    id: string;
    labels: string[];
    properties: Record<string, any>;
  }>;
  relationships: Array<{
    id: string;
    type: string;
    startNode: string;
    endNode: string;
    properties: Record<string, any>;
  }>;
  insights: {
    totalNodes: number;
    totalRelationships: number;
    clusters: number;
    centralNodes: string[];
  };
}

export class Neo4jService {
  private driver: Driver;

  constructor() {
    this.driver = getNeo4jDriver();
  }

  /**
   * 执行Cypher查询
   */
  private async runQuery(cypher: string, parameters: Record<string, any> = {}): Promise<Result> {
    const session = this.driver.session();
    try {
      const result = await session.run(cypher, parameters);
      return result;
    } catch (error) {
      dbLogger.error('Neo4j query failed:', { cypher, parameters, error });
      throw new CustomError('图数据库查询失败', 500);
    } finally {
      await session.close();
    }
  }

  /**
   * 创建企业节点
   */
  async createEnterpriseNode(enterprise: EnterpriseNode): Promise<void> {
    const cypher = `
      MERGE (e:Enterprise {id: $id})
      SET e.name = $name,
          e.industry = $industry,
          e.location = $location,
          e.stage = $stage,
          e.size = $size,
          e.score = $score,
          e.updatedAt = datetime()
      RETURN e
    `;

    try {
      await this.runQuery(cypher, enterprise);
      dbLogger.info('Created enterprise node', { id: enterprise.id, name: enterprise.name });
    } catch (error) {
      dbLogger.error('Failed to create enterprise node:', error);
      throw error;
    }
  }

  /**
   * 创建行业节点
   */
  async createIndustryNode(industry: string): Promise<void> {
    const cypher = `
      MERGE (i:Industry {name: $industry})
      SET i.updatedAt = datetime()
      RETURN i
    `;

    try {
      await this.runQuery(cypher, { industry });
      dbLogger.info('Created industry node', { industry });
    } catch (error) {
      dbLogger.error('Failed to create industry node:', error);
      throw error;
    }
  }

  /**
   * 创建地区节点
   */
  async createLocationNode(location: string): Promise<void> {
    const cypher = `
      MERGE (l:Location {name: $location})
      SET l.updatedAt = datetime()
      RETURN l
    `;

    try {
      await this.runQuery(cypher, { location });
      dbLogger.info('Created location node', { location });
    } catch (error) {
      dbLogger.error('Failed to create location node:', error);
      throw error;
    }
  }

  /**
   * 创建企业关系
   */
  async createRelationship(
    fromId: string,
    toId: string,
    relationshipType: string,
    properties: Record<string, any> = {}
  ): Promise<void> {
    const cypher = `
      MATCH (a:Enterprise {id: $fromId})
      MATCH (b:Enterprise {id: $toId})
      MERGE (a)-[r:${relationshipType}]->(b)
      SET r += $properties
      SET r.updatedAt = datetime()
      RETURN r
    `;

    try {
      await this.runQuery(cypher, { fromId, toId, properties });
      dbLogger.info('Created relationship', { fromId, toId, type: relationshipType });
    } catch (error) {
      dbLogger.error('Failed to create relationship:', error);
      throw error;
    }
  }

  /**
   * 建立企业与行业的关系
   */
  async linkEnterpriseToIndustry(enterpriseId: string, industry: string): Promise<void> {
    const cypher = `
      MATCH (e:Enterprise {id: $enterpriseId})
      MATCH (i:Industry {name: $industry})
      MERGE (e)-[r:BELONGS_TO]->(i)
      SET r.updatedAt = datetime()
      RETURN r
    `;

    try {
      await this.runQuery(cypher, { enterpriseId, industry });
      dbLogger.info('Linked enterprise to industry', { enterpriseId, industry });
    } catch (error) {
      dbLogger.error('Failed to link enterprise to industry:', error);
      throw error;
    }
  }

  /**
   * 建立企业与地区的关系
   */
  async linkEnterpriseToLocation(enterpriseId: string, location: string): Promise<void> {
    const cypher = `
      MATCH (e:Enterprise {id: $enterpriseId})
      MATCH (l:Location {name: $location})
      MERGE (e)-[r:LOCATED_IN]->(l)
      SET r.updatedAt = datetime()
      RETURN r
    `;

    try {
      await this.runQuery(cypher, { enterpriseId, location });
      dbLogger.info('Linked enterprise to location', { enterpriseId, location });
    } catch (error) {
      dbLogger.error('Failed to link enterprise to location:', error);
      throw error;
    }
  }

  /**
   * 查找相似企业
   */
  async findSimilarEnterprises(enterpriseId: string, limit: number = 10): Promise<EnterpriseNode[]> {
    const cypher = `
      MATCH (e:Enterprise {id: $enterpriseId})
      MATCH (similar:Enterprise)
      WHERE similar.id <> e.id
        AND (similar.industry = e.industry 
             OR similar.location = e.location 
             OR similar.stage = e.stage
             OR similar.size = e.size)
      WITH similar, 
           CASE WHEN similar.industry = e.industry THEN 3 ELSE 0 END +
           CASE WHEN similar.location = e.location THEN 2 ELSE 0 END +
           CASE WHEN similar.stage = e.stage THEN 2 ELSE 0 END +
           CASE WHEN similar.size = e.size THEN 1 ELSE 0 END AS similarity_score
      WHERE similarity_score > 0
      RETURN similar.id as id, similar.name as name, similar.industry as industry,
             similar.location as location, similar.stage as stage, 
             similar.size as size, similar.score as score, similarity_score
      ORDER BY similarity_score DESC, similar.score DESC
      LIMIT $limit
    `;

    try {
      const result = await this.runQuery(cypher, { enterpriseId, limit });
      const enterprises = result.records.map(record => ({
        id: record.get('id'),
        name: record.get('name'),
        industry: record.get('industry'),
        location: record.get('location'),
        stage: record.get('stage'),
        size: record.get('size'),
        score: record.get('score')
      }));

      dbLogger.info('Found similar enterprises', { enterpriseId, count: enterprises.length });
      return enterprises;
    } catch (error) {
      dbLogger.error('Failed to find similar enterprises:', error);
      throw error;
    }
  }

  /**
   * 获取行业网络分析
   */
  async getIndustryNetwork(industry: string): Promise<GraphAnalysisResult> {
    const cypher = `
      MATCH (e:Enterprise)-[:BELONGS_TO]->(i:Industry {name: $industry})
      OPTIONAL MATCH (e)-[r]-(connected)
      RETURN e, r, connected
      LIMIT 100
    `;

    try {
      const result = await this.runQuery(cypher, { industry });
      
      const nodes = new Map();
      const relationships: any[] = [];

      result.records.forEach(record => {
        const enterprise = record.get('e');
        const relationship = record.get('r');
        const connected = record.get('connected');

        // Add enterprise node
        if (enterprise && !nodes.has(enterprise.properties.id)) {
          nodes.set(enterprise.properties.id, {
            id: enterprise.properties.id,
            labels: enterprise.labels,
            properties: enterprise.properties
          });
        }

        // Add connected node
        if (connected && !nodes.has(connected.properties.id || connected.properties.name)) {
          const nodeId = connected.properties.id || connected.properties.name;
          nodes.set(nodeId, {
            id: nodeId,
            labels: connected.labels,
            properties: connected.properties
          });
        }

        // Add relationship
        if (relationship) {
          relationships.push({
            id: relationship.identity.toString(),
            type: relationship.type,
            startNode: relationship.start.toString(),
            endNode: relationship.end.toString(),
            properties: relationship.properties
          });
        }
      });

      const analysisResult: GraphAnalysisResult = {
        nodes: Array.from(nodes.values()),
        relationships: relationships,
        insights: {
          totalNodes: nodes.size,
          totalRelationships: relationships.length,
          clusters: Math.ceil(nodes.size / 10), // 简单的聚类估算
          centralNodes: Array.from(nodes.values())
            .filter(node => node.labels.includes('Enterprise'))
            .sort((a, b) => (b.properties.score || 0) - (a.properties.score || 0))
            .slice(0, 5)
            .map(node => node.properties.name)
        }
      };

      dbLogger.info('Generated industry network analysis', { 
        industry, 
        nodes: analysisResult.insights.totalNodes,
        relationships: analysisResult.insights.totalRelationships
      });

      return analysisResult;
    } catch (error) {
      dbLogger.error('Failed to get industry network:', error);
      throw error;
    }
  }

  /**
   * 清理图数据库
   */
  async clearDatabase(): Promise<void> {
    const cypher = 'MATCH (n) DETACH DELETE n';
    
    try {
      await this.runQuery(cypher);
      dbLogger.info('Cleared Neo4j database');
    } catch (error) {
      dbLogger.error('Failed to clear Neo4j database:', error);
      throw error;
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getDatabaseStats(): Promise<{
    nodeCount: number;
    relationshipCount: number;
    labels: string[];
    relationshipTypes: string[];
  }> {
    try {
      const [nodeResult, relResult, labelResult, typeResult] = await Promise.all([
        this.runQuery('MATCH (n) RETURN count(n) as count'),
        this.runQuery('MATCH ()-[r]->() RETURN count(r) as count'),
        this.runQuery('CALL db.labels()'),
        this.runQuery('CALL db.relationshipTypes()')
      ]);

      return {
        nodeCount: nodeResult.records[0]?.get('count').toNumber() || 0,
        relationshipCount: relResult.records[0]?.get('count').toNumber() || 0,
        labels: labelResult.records.map(record => record.get('label')),
        relationshipTypes: typeResult.records.map(record => record.get('relationshipType'))
      };
    } catch (error) {
      dbLogger.error('Failed to get database stats:', error);
      throw error;
    }
  }
}

export const neo4jService = new Neo4jService();
