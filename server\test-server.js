const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3002;

// Middleware
app.use(cors({
  origin: 'http://localhost:5174',
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      ai: 'operational',
      database: 'connected'
    }
  });
});

// AI status endpoint
app.get('/api/ai/status', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'operational',
      services: {
        deepseek: {
          available: true,
          model: 'deepseek-reasoner'
        },
        backup: {
          available: true,
          model: 'gpt-3.5-turbo'
        },
        tencent: {
          available: true,
          service: 'image-generation'
        }
      },
      timestamp: new Date().toISOString()
    }
  });
});

// Enterprises endpoint
app.get('/api/enterprises', (req, res) => {
  const mockEnterprises = [
    {
      id: '1',
      name: '腾讯科技',
      industry: '互联网',
      size: '10000+人',
      stage: '成熟期',
      location: '深圳',
      revenue: '500亿+',
      employees: 85000,
      founded: 1998,
      status: '行业领导者',
      score: 95,
      description: '中国领先的互联网增值服务提供商',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      name: '阿里巴巴',
      industry: '电子商务',
      size: '10000+人',
      stage: '成熟期',
      location: '杭州',
      revenue: '700亿+',
      employees: 120000,
      founded: 1999,
      status: '行业领导者',
      score: 98,
      description: '全球领先的电子商务和云计算公司',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  res.json({
    success: true,
    data: {
      enterprises: mockEnterprises,
      pagination: {
        total: mockEnterprises.length,
        page: 1,
        limit: 20,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    }
  });
});

// Enterprise stats endpoint
app.get('/api/enterprises/stats/overview', (req, res) => {
  res.json({
    success: true,
    data: {
      total: 3,
      byIndustry: {
        '互联网': 2,
        '电子商务': 1
      },
      byStage: {
        '成熟期': 2,
        '成长期': 1
      },
      byLocation: {
        '深圳': 1,
        '杭州': 1,
        '北京': 1
      },
      averageScore: 95
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Test Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 CORS enabled for: http://localhost:5174`);
  console.log(`🎯 Phase 1: Basic API endpoints ready!`);
});

module.exports = app;
