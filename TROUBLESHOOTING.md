# 🔧 故障排除指南

## 常见问题解决方案

### 1. 后端3001端口返回404错误

**问题描述**：访问 http://localhost:3001 返回 `{"success":false,"error":{"code":"NOT_FOUND","message":"路径 / 未找到"}}`

**解决方案**：
```bash
# 1. 确认后端服务器正在运行
cd server
npm run dev

# 2. 检查端口配置
# 确认 server/.env 中 PORT=3001
# 确认前端 .env 中 VITE_API_URL=http://localhost:3001

# 3. 测试连接
curl http://localhost:3001/
curl http://localhost:3001/health
```

**预期结果**：
- 根路径 `/` 应返回API信息和功能列表
- 健康检查 `/health` 应返回服务状态

### 2. 前后端端口不匹配

**问题描述**：前端无法连接到后端API

**检查清单**：
- [ ] 后端端口：server/.env 中 `PORT=3001`
- [ ] 前端API配置：.env 中 `VITE_API_URL=http://localhost:3001`
- [ ] CORS配置：server/.env 中 `CORS_ORIGIN=http://localhost:5173`

**修复命令**：
```bash
# 重启服务以应用新配置
npm run dev
```

### 3. 数据库连接失败

**问题描述**：后端启动时数据库连接错误

**解决步骤**：
```bash
# 1. 启动数据库服务
start-databases.bat  # Windows
./start-databases.sh # Linux/Mac

# 2. 等待数据库启动完成（约30秒）

# 3. 初始化数据库
cd server
npm run db:setup

# 4. 重启后端服务
npm run dev
```

### 4. AI API配置问题

**问题描述**：AI分析功能不可用

**检查配置**：
```bash
# 检查 server/.env 中的AI API配置
DEEPSEEK_API_KEY=***********************************
BACKUP_AI_API_KEY=sk-HZ5i3NCFjxNg81vDE178F940524b495a928715D450B7372e
```

**测试AI服务**：
```bash
curl http://localhost:3001/api/ai/status
```

### 5. 前端页面空白或加载失败

**问题描述**：前端页面无法正常显示

**解决步骤**：
```bash
# 1. 检查前端服务是否运行
npm run dev:client

# 2. 检查浏览器控制台错误
# 按F12打开开发者工具查看错误信息

# 3. 清除缓存并重新加载
# Ctrl+Shift+R 强制刷新

# 4. 重新安装依赖
npm install
```

## 快速诊断命令

### 检查服务状态
```bash
# 检查端口占用
netstat -ano | findstr :3001  # Windows
lsof -i :3001                 # Linux/Mac

# 检查进程
tasklist | findstr node       # Windows
ps aux | grep node            # Linux/Mac
```

### 测试API连接
```bash
# 运行连接测试脚本
test-connection.bat  # Windows

# 手动测试主要API
curl http://localhost:3001/
curl http://localhost:3001/health
curl http://localhost:3001/api/enterprises
curl http://localhost:3001/api/ai/status
```

### 查看日志
```bash
# 后端日志
tail -f server/logs/combined.log    # Linux/Mac
type server\logs\combined.log       # Windows

# 数据库日志
docker compose -f docker-compose.dev.yml logs
```

## 完整重启流程

如果遇到复杂问题，可以按以下步骤完全重启：

```bash
# 1. 停止所有服务
# 按 Ctrl+C 停止开发服务器
docker compose -f docker-compose.dev.yml down

# 2. 清理缓存
npm run clean  # 如果有这个脚本
rm -rf node_modules server/node_modules  # 删除依赖
rm -rf .next .vite dist                   # 删除构建缓存

# 3. 重新安装依赖
npm install
cd server && npm install && cd ..

# 4. 重启数据库
start-databases.bat  # Windows
./start-databases.sh # Linux/Mac

# 5. 等待数据库启动（30秒）

# 6. 初始化数据库
cd server && npm run db:setup && cd ..

# 7. 启动应用
npm run dev
```

## 环境检查清单

### 必需软件
- [ ] Node.js 18+ 已安装
- [ ] npm 或 yarn 已安装
- [ ] Docker Desktop 已安装并运行
- [ ] Git 已安装（可选）

### 端口检查
- [ ] 3001 端口未被占用（后端）
- [ ] 5173 端口未被占用（前端）
- [ ] 27017 端口未被占用（MongoDB）
- [ ] 7687 端口未被占用（Neo4j）
- [ ] 5432 端口未被占用（PostgreSQL）

### 配置文件检查
- [ ] server/.env 文件存在且配置正确
- [ ] .env 文件存在且配置正确
- [ ] package.json 依赖完整
- [ ] server/package.json 依赖完整

## 获取帮助

如果问题仍然存在，请：

1. **收集信息**：
   - 操作系统版本
   - Node.js 版本 (`node -v`)
   - npm 版本 (`npm -v`)
   - 错误信息截图
   - 浏览器控制台错误

2. **检查日志**：
   - 后端日志：server/logs/
   - 前端控制台错误
   - 数据库连接日志

3. **尝试最小化复现**：
   - 使用全新的终端窗口
   - 按照快速启动指南重新操作
   - 记录每一步的输出结果

---

**💡 提示**：大多数问题都是由端口配置不匹配或数据库未启动引起的。请优先检查这两个方面。
