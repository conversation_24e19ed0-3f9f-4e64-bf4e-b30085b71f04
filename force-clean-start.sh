#!/bin/bash

echo "🧹 强制清理并重新启动企业画像师平台"
echo "=========================================="

echo "📋 强制关闭所有相关进程..."

# 强制关闭所有node进程
echo "🔄 关闭Node.js进程..."
pkill -f node 2>/dev/null && echo "✅ Node.js进程已关闭" || echo "ℹ️  没有发现Node.js进程"

# 强制关闭npm进程
echo "🔄 关闭npm进程..."
pkill -f npm 2>/dev/null && echo "✅ npm进程已关闭" || echo "ℹ️  没有发现npm进程"

# 强制关闭可能的tsx进程
echo "🔄 关闭tsx进程..."
pkill -f tsx 2>/dev/null && echo "✅ tsx进程已关闭" || echo "ℹ️  没有发现tsx进程"

# 强制关闭可能的vite进程
echo "🔄 关闭vite进程..."
pkill -f vite 2>/dev/null && echo "✅ vite进程已关闭" || echo "ℹ️  没有发现vite进程"

echo "🕐 等待进程完全关闭..."
sleep 3

# 强制释放端口
echo "🔍 强制释放端口..."

# 释放5173端口
PORT_5173_PID=$(lsof -ti:5173 2>/dev/null)
if [ ! -z "$PORT_5173_PID" ]; then
    echo "🔄 释放端口5173，PID: $PORT_5173_PID"
    kill -9 $PORT_5173_PID 2>/dev/null
else
    echo "✅ 端口5173已释放"
fi

# 释放3001端口
PORT_3001_PID=$(lsof -ti:3001 2>/dev/null)
if [ ! -z "$PORT_3001_PID" ]; then
    echo "🔄 释放端口3001，PID: $PORT_3001_PID"
    kill -9 $PORT_3001_PID 2>/dev/null
else
    echo "✅ 端口3001已释放"
fi

echo "🕐 等待端口释放..."
sleep 2

# 验证端口状态
echo "📋 验证端口状态..."
if lsof -i:5173 >/dev/null 2>&1; then
    echo "⚠️  端口5173仍被占用"
else
    echo "✅ 端口5173已释放"
fi

if lsof -i:3001 >/dev/null 2>&1; then
    echo "⚠️  端口3001仍被占用"
else
    echo "✅ 端口3001已释放"
fi

echo ""
echo "🧹 清理临时文件和缓存..."

# 清理npm缓存
echo "🔄 清理npm缓存..."
npm cache clean --force 2>/dev/null || echo "npm缓存清理完成"

# 备份并清理日志文件
if [ -d "server/logs" ]; then
    echo "📝 清理日志文件..."
    mkdir -p server/logs/backup
    if ls server/logs/*.log 1> /dev/null 2>&1; then
        cp server/logs/*.log server/logs/backup/ 2>/dev/null
        rm server/logs/*.log 2>/dev/null
    fi
fi

# 创建必要的目录
mkdir -p server/logs

echo ""
echo "🔧 重新检查环境..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 未找到Node.js，请先安装Node.js 18+"
    exit 1
else
    echo "✅ Node.js版本: $(node -v)"
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ 未找到npm"
    exit 1
else
    echo "✅ npm版本: $(npm -v)"
fi

echo ""
echo "📦 重新安装依赖..."

# 删除并重新安装前端依赖
if [ -d "node_modules" ]; then
    echo "🗑️  删除前端node_modules..."
    rm -rf node_modules
fi
echo "📦 重新安装前端依赖..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ 前端依赖安装失败"
    exit 1
fi

# 删除并重新安装后端依赖
cd server
if [ -d "node_modules" ]; then
    echo "🗑️  删除后端node_modules..."
    rm -rf node_modules
fi
echo "📦 重新安装后端依赖..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ 后端依赖安装失败"
    exit 1
fi
cd ..

echo ""
echo "🔧 检查配置文件..."
if [ ! -f "server/.env" ]; then
    echo "📄 创建环境配置文件..."
    cp server/.env.example server/.env
    echo "✅ 已创建server/.env文件"
fi

echo ""
echo "🎯 启动全新的开发环境..."
echo "=========================================="
echo "前端地址: http://localhost:5173"
echo "后端地址: http://localhost:3001"
echo "健康检查: http://localhost:3001/health"
echo "AI状态: http://localhost:3001/api/ai/status"
echo ""
echo "💡 提示："
echo "- 这是全新的环境，所有依赖都已重新安装"
echo "- 如果仍有问题，请检查防火墙设置"
echo "- 查看实时日志：server/logs/combined.log"
echo "- 按 Ctrl+C 停止服务器"
echo "=========================================="
echo ""

# 启动开发服务器
npm run dev
