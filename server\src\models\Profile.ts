import mongoose, { Document, Schema } from 'mongoose';

export interface IProfile extends Document {
  name: string;
  description?: string;
  targetConditions: {
    industry?: string[];
    size?: string[];
    stage?: string[];
    location?: string[];
    revenue?: {
      min?: string;
      max?: string;
    };
    employees?: {
      min?: number;
      max?: number;
    };
    founded?: {
      min?: number;
      max?: number;
    };
    customCriteria?: Array<{
      field: string;
      operator: 'equals' | 'contains' | 'greater' | 'less' | 'between';
      value: any;
    }>;
  };
  searchMethods?: {
    dataSources?: string[];
    keywords?: string[];
    searchDepth?: 'basic' | 'detailed' | 'comprehensive';
    autoUpdate?: boolean;
    updateFrequency?: 'daily' | 'weekly' | 'monthly';
  };
  analysisConfig?: {
    aiModel?: string;
    analysisDepth?: 'basic' | 'standard' | 'comprehensive';
    includeRiskAssessment?: boolean;
    includeCompetitorAnalysis?: boolean;
    customPrompts?: string[];
  };
  results?: {
    totalMatches?: number;
    highPriorityMatches?: number;
    mediumPriorityMatches?: number;
    lowPriorityMatches?: number;
    analysisScore?: number;
    lastUpdated?: Date;
    enterprises?: mongoose.Types.ObjectId[];
  };
  status: 'draft' | 'active' | 'completed' | 'archived';
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

const ProfileSchema = new Schema<IProfile>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
    index: true
  },
  description: {
    type: String,
    maxlength: 1000
  },
  targetConditions: {
    industry: [{
      type: String,
      trim: true
    }],
    size: [{
      type: String,
      enum: ['1-10人', '11-50人', '51-100人', '101-500人', '501-1000人', '1000-5000人', '5000+人', '10000+人']
    }],
    stage: [{
      type: String,
      enum: ['初创期', '成长期', '成熟期', '转型期', '衰退期']
    }],
    location: [{
      type: String,
      trim: true
    }],
    revenue: {
      min: String,
      max: String
    },
    employees: {
      min: {
        type: Number,
        min: 0
      },
      max: {
        type: Number,
        min: 0
      }
    },
    founded: {
      min: {
        type: Number,
        min: 1800
      },
      max: {
        type: Number,
        max: new Date().getFullYear()
      }
    },
    customCriteria: [{
      field: {
        type: String,
        required: true
      },
      operator: {
        type: String,
        enum: ['equals', 'contains', 'greater', 'less', 'between'],
        required: true
      },
      value: Schema.Types.Mixed
    }]
  },
  searchMethods: {
    dataSources: [{
      type: String,
      enum: ['企查查', '天眼查', '启信宝', '官网', '公开报告', '新闻资讯', '社交媒体']
    }],
    keywords: [{
      type: String,
      trim: true
    }],
    searchDepth: {
      type: String,
      enum: ['basic', 'detailed', 'comprehensive'],
      default: 'standard'
    },
    autoUpdate: {
      type: Boolean,
      default: false
    },
    updateFrequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'weekly'
    }
  },
  analysisConfig: {
    aiModel: {
      type: String,
      default: 'deepseek-reasoner'
    },
    analysisDepth: {
      type: String,
      enum: ['basic', 'standard', 'comprehensive'],
      default: 'standard'
    },
    includeRiskAssessment: {
      type: Boolean,
      default: true
    },
    includeCompetitorAnalysis: {
      type: Boolean,
      default: false
    },
    customPrompts: [{
      type: String,
      maxlength: 500
    }]
  },
  results: {
    totalMatches: {
      type: Number,
      default: 0
    },
    highPriorityMatches: {
      type: Number,
      default: 0
    },
    mediumPriorityMatches: {
      type: Number,
      default: 0
    },
    lowPriorityMatches: {
      type: Number,
      default: 0
    },
    analysisScore: {
      type: Number,
      min: 0,
      max: 100
    },
    lastUpdated: Date,
    enterprises: [{
      type: Schema.Types.ObjectId,
      ref: 'Enterprise'
    }]
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'completed', 'archived'],
    default: 'draft',
    index: true
  },
  createdBy: {
    type: String,
    trim: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
ProfileSchema.index({ name: 'text', description: 'text' });
ProfileSchema.index({ status: 1, createdAt: -1 });
ProfileSchema.index({ 'targetConditions.industry': 1 });
ProfileSchema.index({ 'results.analysisScore': -1 });

// 虚拟字段
ProfileSchema.virtual('matchRate').get(function() {
  if (this.results?.totalMatches && this.results.totalMatches > 0) {
    return Math.round((this.results.highPriorityMatches || 0) / this.results.totalMatches * 100);
  }
  return 0;
});

// 静态方法
ProfileSchema.statics.findActive = function() {
  return this.find({ status: 'active' }).sort({ updatedAt: -1 });
};

ProfileSchema.statics.findByIndustry = function(industry: string) {
  return this.find({ 'targetConditions.industry': industry });
};

// 实例方法
ProfileSchema.methods.updateResults = function(results: any) {
  this.results = {
    ...this.results,
    ...results,
    lastUpdated: new Date()
  };
  return this.save();
};

ProfileSchema.methods.activate = function() {
  this.status = 'active';
  return this.save();
};

export const Profile = mongoose.model<IProfile>('Profile', ProfileSchema);
