@echo off
echo 🧹 强制清理并重新启动企业画像师平台
echo ==========================================

echo 📋 强制关闭所有相关进程...

REM 强制关闭所有node进程
echo 🔄 关闭Node.js进程...
taskkill /F /IM node.exe 2>NUL
if %errorlevel% equ 0 (
    echo ✅ Node.js进程已关闭
) else (
    echo ℹ️  没有发现Node.js进程
)

REM 强制关闭tsx进程
echo 🔄 关闭tsx进程...
taskkill /F /IM tsx.exe 2>NUL
if %errorlevel% equ 0 (
    echo ✅ tsx进程已关闭
) else (
    echo ℹ️  没有发现tsx进程
)

REM 强制关闭npm进程
echo 🔄 关闭npm进程...
taskkill /F /IM npm.exe 2>NUL
if %errorlevel% equ 0 (
    echo ✅ npm进程已关闭
) else (
    echo ℹ️  没有发现npm进程
)

REM 强制关闭可能的cmd进程（运行npm的）
echo 🔄 关闭可能的开发服务器进程...
for /f "tokens=2" %%i in ('tasklist /FI "WINDOWTITLE eq npm*" /FO CSV ^| findstr /V "PID"') do (
    if not "%%i"=="" (
        taskkill /F /PID %%i 2>NUL
        echo ✅ 关闭了PID %%i
    )
)

echo 🕐 等待进程完全关闭...
timeout /t 5 /nobreak >NUL

REM 强制释放端口
echo 🔍 强制释放端口...

REM 释放5173端口
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5173 2^>NUL') do (
    if not "%%a"=="0" (
        echo 🔄 释放端口5173，PID: %%a
        taskkill /F /PID %%a 2>NUL
    )
)

REM 释放3001端口
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001 2^>NUL') do (
    if not "%%a"=="0" (
        echo 🔄 释放端口3001，PID: %%a
        taskkill /F /PID %%a 2>NUL
    )
)

echo 🕐 等待端口释放...
timeout /t 3 /nobreak >NUL

REM 验证端口状态
echo 📋 验证端口状态...
netstat -ano | findstr :5173 >NUL
if %errorlevel% equ 0 (
    echo ⚠️  端口5173仍被占用
) else (
    echo ✅ 端口5173已释放
)

netstat -ano | findstr :3001 >NUL
if %errorlevel% equ 0 (
    echo ⚠️  端口3001仍被占用
) else (
    echo ✅ 端口3001已释放
)

echo.
echo 🧹 清理临时文件和缓存...

REM 清理npm缓存
echo 🔄 清理npm缓存...
npm cache clean --force 2>NUL

REM 备份并清理日志文件
if exist "server\logs" (
    echo 📝 清理日志文件...
    if not exist "server\logs\backup" mkdir "server\logs\backup"
    if exist "server\logs\*.log" (
        copy "server\logs\*.log" "server\logs\backup\" >NUL 2>&1
        del "server\logs\*.log" >NUL 2>&1
    )
)

REM 创建必要的目录
if not exist "server\logs" mkdir "server\logs"

echo.
echo 🔧 重新检查环境...

REM 检查Node.js
node -v >NUL 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js 18+
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node -v') do echo ✅ Node.js版本: %%i
)

REM 检查npm
npm -v >NUL 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到npm
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm -v') do echo ✅ npm版本: %%i
)

echo.
echo 📦 重新安装依赖...

REM 删除并重新安装前端依赖
if exist "node_modules" (
    echo 🗑️  删除前端node_modules...
    rmdir /s /q "node_modules" 2>NUL
)
echo 📦 重新安装前端依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)

REM 删除并重新安装后端依赖
cd server
if exist "node_modules" (
    echo 🗑️  删除后端node_modules...
    rmdir /s /q "node_modules" 2>NUL
)
echo 📦 重新安装后端依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo.
echo 🔧 检查配置文件...
if not exist "server\.env" (
    echo 📄 创建环境配置文件...
    copy "server\.env.example" "server\.env"
    echo ✅ 已创建server\.env文件
)

echo.
echo 🎯 启动全新的开发环境...
echo ==========================================
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:3001
echo 健康检查: http://localhost:3001/health
echo AI状态: http://localhost:3001/api/ai/status
echo.
echo 💡 提示：
echo - 这是全新的环境，所有依赖都已重新安装
echo - 如果仍有问题，请检查防火墙和杀毒软件设置
echo - 查看实时日志：server\logs\combined.log
echo - 按 Ctrl+C 停止服务器
echo ==========================================
echo.

REM 启动开发服务器
npm run dev
