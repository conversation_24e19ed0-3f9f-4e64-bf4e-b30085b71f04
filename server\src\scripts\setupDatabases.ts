import { connectDatabases, closeDatabaseConnections } from '../config/database.js';
import { seedDatabase } from './seedDatabase.js';
import { neo4jService } from '../services/neo4jService.js';
import { postgresService } from '../services/postgresService.js';
import { Enterprise } from '../models/Enterprise.js';
import { dbLogger } from '../utils/logger.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function setupDatabases() {
  try {
    dbLogger.info('🚀 Starting database setup...');

    // Connect to all databases
    await connectDatabases();

    // Seed MongoDB with sample data
    dbLogger.info('📦 Seeding MongoDB...');
    await seedDatabase();

    // Setup Neo4j graph data
    dbLogger.info('🕸️ Setting up Neo4j graph data...');
    await setupNeo4jData();

    // Setup PostgreSQL analytics data
    dbLogger.info('📊 Setting up PostgreSQL analytics data...');
    await setupPostgresData();

    dbLogger.info('✅ Database setup completed successfully!');
    
    // Display setup summary
    await displaySetupSummary();

  } catch (error) {
    dbLogger.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await closeDatabaseConnections();
    process.exit(0);
  }
}

async function setupNeo4jData() {
  try {
    // Clear existing data
    await neo4jService.clearDatabase();

    // Get enterprises from MongoDB
    const enterprises = await Enterprise.find().limit(20);

    // Create industry and location nodes
    const industries = [...new Set(enterprises.map(e => e.industry))];
    const locations = [...new Set(enterprises.map(e => e.location).filter(Boolean))];

    for (const industry of industries) {
      await neo4jService.createIndustryNode(industry);
    }

    for (const location of locations) {
      if (location) {
        await neo4jService.createLocationNode(location);
      }
    }

    // Create enterprise nodes and relationships
    for (const enterprise of enterprises) {
      const enterpriseNode = {
        id: enterprise._id.toString(),
        name: enterprise.name,
        industry: enterprise.industry,
        location: enterprise.location || '',
        stage: enterprise.stage || '',
        size: enterprise.size || '',
        score: enterprise.score || 0
      };

      await neo4jService.createEnterpriseNode(enterpriseNode);
      await neo4jService.linkEnterpriseToIndustry(enterpriseNode.id, enterprise.industry);
      
      if (enterprise.location) {
        await neo4jService.linkEnterpriseToLocation(enterpriseNode.id, enterprise.location);
      }
    }

    // Create some sample relationships between enterprises
    const internetEnterprises = enterprises.filter(e => e.industry === '互联网');
    for (let i = 0; i < internetEnterprises.length - 1; i++) {
      const current = internetEnterprises[i];
      const next = internetEnterprises[i + 1];
      
      if (current.location === next.location) {
        await neo4jService.createRelationship(
          current._id.toString(),
          next._id.toString(),
          'COMPETES_WITH',
          { strength: 0.7, reason: '同地区同行业竞争' }
        );
      }
    }

    dbLogger.info('Neo4j graph data setup completed');
  } catch (error) {
    dbLogger.error('Failed to setup Neo4j data:', error);
    throw error;
  }
}

async function setupPostgresData() {
  try {
    // Get enterprises from MongoDB
    const enterprises = await Enterprise.find().limit(10);

    // Create sample analytics results
    for (const enterprise of enterprises) {
      // Enterprise profile analysis
      await postgresService.saveAnalyticsResult({
        enterprise_id: enterprise._id.toString(),
        analysis_type: 'enterprise_profile',
        result_data: {
          overallScore: enterprise.score || Math.floor(Math.random() * 30) + 70,
          dimensions: [
            {
              name: '业务成熟度',
              score: Math.floor(Math.random() * 20) + 80,
              factors: ['成立时间较长', '业务模式成熟', '市场地位稳固'],
              recommendations: ['关注新业务拓展机会', '评估数字化转型需求']
            },
            {
              name: '成长潜力',
              score: Math.floor(Math.random() * 20) + 75,
              factors: ['行业增长迅速', '技术创新能力强'],
              recommendations: ['重点关注技术升级需求']
            }
          ],
          insights: [
            '该企业正处于快速发展期',
            '决策层重视技术创新',
            '财务状况良好'
          ],
          risks: [
            {
              type: '市场风险',
              level: 'medium',
              description: '行业竞争激烈',
              mitigation: '持续创新保持优势'
            }
          ]
        },
        confidence_score: 0.85
      });

      // Role analysis
      await postgresService.saveAnalyticsResult({
        enterprise_id: enterprise._id.toString(),
        analysis_type: 'role_analysis',
        result_data: {
          roles: {
            decisionMaker: {
              title: 'CEO/CTO',
              influence: 95,
              characteristics: ['技术背景深厚', '注重ROI']
            },
            influencer: {
              title: '技术总监',
              influence: 85,
              characteristics: ['技术专业性强', '关注技术细节']
            }
          }
        },
        confidence_score: 0.78
      });

      // Sample user activity
      await postgresService.logUserActivity({
        user_id: 'system',
        action: 'enterprise_analysis',
        resource_type: 'enterprise',
        resource_id: enterprise._id.toString(),
        metadata: {
          analysis_type: 'automated_setup',
          duration: Math.floor(Math.random() * 5000) + 1000
        },
        ip_address: '127.0.0.1',
        user_agent: 'Database Setup Script'
      });
    }

    // Create sample reports
    await postgresService.saveReport({
      name: '企业画像分析报告',
      description: '基于AI分析的企业画像综合报告',
      report_type: 'enterprise_analysis',
      data: {
        summary: {
          totalEnterprises: enterprises.length,
          averageScore: enterprises.reduce((sum, e) => sum + (e.score || 0), 0) / enterprises.length,
          topIndustries: ['互联网', '电子商务', '智能硬件']
        },
        recommendations: [
          '重点关注互联网行业的成长期企业',
          '加强与头部企业的合作关系',
          '关注新兴技术领域的投资机会'
        ]
      },
      generated_by: 'system'
    });

    dbLogger.info('PostgreSQL analytics data setup completed');
  } catch (error) {
    dbLogger.error('Failed to setup PostgreSQL data:', error);
    throw error;
  }
}

async function displaySetupSummary() {
  try {
    // MongoDB stats
    const enterpriseCount = await Enterprise.countDocuments();
    
    // Neo4j stats
    const neo4jStats = await neo4jService.getDatabaseStats();
    
    // PostgreSQL stats
    const postgresStats = await postgresService.getAnalyticsStats();

    console.log('\n🎉 数据库设置完成摘要:');
    console.log('================================');
    console.log(`📊 MongoDB:`);
    console.log(`   - 企业数量: ${enterpriseCount}`);
    console.log(`🕸️ Neo4j:`);
    console.log(`   - 节点数量: ${neo4jStats.nodeCount}`);
    console.log(`   - 关系数量: ${neo4jStats.relationshipCount}`);
    console.log(`   - 标签类型: ${neo4jStats.labels.join(', ')}`);
    console.log(`📈 PostgreSQL:`);
    console.log(`   - 分析记录: ${postgresStats.totalAnalyses}`);
    console.log(`   - 平均置信度: ${postgresStats.averageConfidence.toFixed(2)}`);
    console.log('================================');
    console.log('✅ 所有数据库已准备就绪！');
    console.log('🚀 现在可以启动应用程序了');
  } catch (error) {
    dbLogger.error('Failed to display setup summary:', error);
  }
}

// Run setup if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDatabases();
}

export { setupDatabases };
