import mongoose, { Document, Schema } from 'mongoose';

export interface IEnterprise extends Document {
  name: string;
  industry: string;
  size?: string;
  stage?: string;
  location?: string;
  revenue?: string;
  employees?: number;
  founded?: number;
  status?: string;
  score?: number;
  description?: string;
  website?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
    address?: string;
  };
  businessInfo?: {
    registrationNumber?: string;
    legalRepresentative?: string;
    registeredCapital?: string;
    businessScope?: string;
  };
  analysisData?: {
    lastAnalyzed?: Date;
    aiInsights?: any;
    matchingScore?: number;
    riskLevel?: 'low' | 'medium' | 'high';
    tags?: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

const EnterpriseSchema = new Schema<IEnterprise>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
    index: true
  },
  industry: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
    index: true
  },
  size: {
    type: String,
    trim: true,
    enum: ['1-10人', '11-50人', '51-100人', '101-500人', '501-1000人', '1000-5000人', '5000+人', '10000+人']
  },
  stage: {
    type: String,
    trim: true,
    enum: ['初创期', '成长期', '成熟期', '转型期', '衰退期'],
    index: true
  },
  location: {
    type: String,
    trim: true,
    maxlength: 100,
    index: true
  },
  revenue: {
    type: String,
    trim: true
  },
  employees: {
    type: Number,
    min: 0,
    index: true
  },
  founded: {
    type: Number,
    min: 1800,
    max: new Date().getFullYear(),
    index: true
  },
  status: {
    type: String,
    default: '待分析',
    enum: ['待分析', '分析中', '已分析', '高优先级', '中优先级', '低优先级', '已联系', '合作中', '已完成']
  },
  score: {
    type: Number,
    min: 0,
    max: 100,
    index: true
  },
  description: {
    type: String,
    maxlength: 2000
  },
  website: {
    type: String,
    trim: true,
    maxlength: 200
  },
  contactInfo: {
    email: {
      type: String,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
    },
    phone: {
      type: String,
      trim: true
    },
    address: {
      type: String,
      trim: true,
      maxlength: 500
    }
  },
  businessInfo: {
    registrationNumber: {
      type: String,
      trim: true,
      unique: true,
      sparse: true
    },
    legalRepresentative: {
      type: String,
      trim: true,
      maxlength: 100
    },
    registeredCapital: {
      type: String,
      trim: true
    },
    businessScope: {
      type: String,
      maxlength: 1000
    }
  },
  analysisData: {
    lastAnalyzed: {
      type: Date
    },
    aiInsights: {
      type: Schema.Types.Mixed
    },
    matchingScore: {
      type: Number,
      min: 0,
      max: 100
    },
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    tags: [{
      type: String,
      trim: true
    }]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
EnterpriseSchema.index({ name: 'text', description: 'text' });
EnterpriseSchema.index({ industry: 1, stage: 1 });
EnterpriseSchema.index({ location: 1, size: 1 });
EnterpriseSchema.index({ score: -1 });
EnterpriseSchema.index({ 'analysisData.lastAnalyzed': -1 });

// 虚拟字段
EnterpriseSchema.virtual('ageInYears').get(function() {
  if (this.founded) {
    return new Date().getFullYear() - this.founded;
  }
  return null;
});

// 中间件
EnterpriseSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.name = this.name.trim();
  }
  next();
});

// 静态方法
EnterpriseSchema.statics.findByIndustry = function(industry: string) {
  return this.find({ industry: new RegExp(industry, 'i') });
};

EnterpriseSchema.statics.findHighPriority = function() {
  return this.find({ score: { $gte: 80 } }).sort({ score: -1 });
};

// 实例方法
EnterpriseSchema.methods.updateAnalysis = function(analysisData: any) {
  this.analysisData = {
    ...this.analysisData,
    ...analysisData,
    lastAnalyzed: new Date()
  };
  return this.save();
};

export const Enterprise = mongoose.model<IEnterprise>('Enterprise', EnterpriseSchema);
