{"name": "enterprise-profiler", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "vite", "dev:server": "cd server && npm run dev", "build": "vite build", "build:server": "cd server && npm run build", "lint": "eslint .", "preview": "vite preview", "setup": "npm install && cd server && npm install", "status": "node check-status.js", "clean": "npm run clean:deps && npm run clean:logs", "clean:deps": "rimraf node_modules server/node_modules", "clean:logs": "rimraf server/logs/*.log"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "concurrently": "^8.2.2", "rimraf": "^5.0.5", "chart.js": "^4.4.1", "react-chartjs-2": "^5.2.0", "d3": "^7.8.5", "recharts": "^2.10.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/d3": "^7.4.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}