import React, { useState, useEffect } from 'react';
import { Brain, TrendingUp, Shield, DollarSign, Users, Image, Download, RefreshCw, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { apiService } from '../services/api';

const AIAnalysis = () => {
  const [selectedEnterprise, setSelectedEnterprise] = useState('');
  const [enterprises, setEnterprises] = useState([]);
  const [analysisTypes, setAnalysisTypes] = useState({
    profile: true,
    risk: true,
    investment: false,
    competitive: false,
    pathway: false
  });
  const [analysisDepth, setAnalysisDepth] = useState('standard');
  const [includeCompetitors, setIncludeCompetitors] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [analysisHistory, setAnalysisHistory] = useState([]);
  const [generatedImages, setGeneratedImages] = useState(null);
  const [isGeneratingImages, setIsGeneratingImages] = useState(false);

  useEffect(() => {
    loadEnterprises();
  }, []);

  const loadEnterprises = async () => {
    try {
      const response = await apiService.getEnterprises({ limit: 50 });
      if (response.success && response.data) {
        setEnterprises(response.data.enterprises);
      }
    } catch (error) {
      console.error('Failed to load enterprises:', error);
    }
  };

  const handleAnalysisTypeChange = (type: string, checked: boolean) => {
    setAnalysisTypes(prev => ({
      ...prev,
      [type]: checked
    }));
  };

  const performAnalysis = async () => {
    if (!selectedEnterprise) {
      alert('请选择要分析的企业');
      return;
    }

    const selectedTypes = Object.entries(analysisTypes)
      .filter(([_, selected]) => selected)
      .map(([type, _]) => type);

    if (selectedTypes.length === 0) {
      alert('请至少选择一种分析类型');
      return;
    }

    setIsAnalyzing(true);
    try {
      const response = await apiService.performIntelligentAnalysis({
        enterpriseId: selectedEnterprise,
        analysisTypes: selectedTypes,
        includeCompetitors,
        depth: analysisDepth
      });

      if (response.success) {
        setAnalysisResults(response.data);
        loadAnalysisHistory();
      } else {
        alert('分析失败: ' + response.error);
      }
    } catch (error) {
      console.error('Analysis failed:', error);
      alert('分析过程中发生错误');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const loadAnalysisHistory = async () => {
    if (!selectedEnterprise) return;

    try {
      const response = await apiService.getAnalysisHistory(selectedEnterprise);
      if (response.success) {
        setAnalysisHistory(response.data.history);
      }
    } catch (error) {
      console.error('Failed to load analysis history:', error);
    }
  };

  const generateVisualizations = async () => {
    if (!selectedEnterprise || !analysisResults) {
      alert('请先完成企业分析');
      return;
    }

    const enterprise = enterprises.find((e: any) => e._id === selectedEnterprise);
    if (!enterprise) return;

    setIsGeneratingImages(true);
    try {
      const response = await apiService.generateBatchVisualization({
        enterpriseName: (enterprise as any).name,
        industry: (enterprise as any).industry,
        stage: (enterprise as any).stage || '成长期',
        analysisData: analysisResults.summary
      });

      if (response.success) {
        setGeneratedImages(response.data);
      } else {
        alert('图像生成失败: ' + response.error);
      }
    } catch (error) {
      console.error('Image generation failed:', error);
      alert('图像生成过程中发生错误');
    } finally {
      setIsGeneratingImages(false);
    }
  };

  const getAnalysisIcon = (type: string) => {
    const icons = {
      profile: Brain,
      risk: Shield,
      investment: DollarSign,
      competitive: TrendingUp,
      pathway: Users
    };
    return icons[type as keyof typeof icons] || Brain;
  };

  const getAnalysisLabel = (type: string) => {
    const labels = {
      profile: '企业画像分析',
      risk: '风险评估',
      investment: '投资价值评估',
      competitive: '竞争分析',
      pathway: '用户路径分析'
    };
    return labels[type as keyof typeof labels] || type;
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">AI智能分析</h1>
          <p className="text-slate-400 mt-1">基于DeepSeek AI的企业深度分析和可视化</p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-green-400">
          <Brain className="w-4 h-4" />
          <span>AI引擎运行中</span>
        </div>
      </div>

      {/* 分析配置 */}
      <div className="bg-slate-800 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-white mb-4">分析配置</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 企业选择 */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              选择企业
            </label>
            <select
              value={selectedEnterprise}
              onChange={(e) => {
                setSelectedEnterprise(e.target.value);
                if (e.target.value) {
                  loadAnalysisHistory();
                }
              }}
              className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white"
            >
              <option value="">请选择企业</option>
              {enterprises.map((enterprise: any) => (
                <option key={enterprise._id} value={enterprise._id}>
                  {enterprise.name} - {enterprise.industry}
                </option>
              ))}
            </select>
          </div>

          {/* 分析深度 */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              分析深度
            </label>
            <select
              value={analysisDepth}
              onChange={(e) => setAnalysisDepth(e.target.value)}
              className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white"
            >
              <option value="basic">基础分析</option>
              <option value="standard">标准分析</option>
              <option value="comprehensive">深度分析</option>
            </select>
          </div>
        </div>

        {/* 分析类型选择 */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-slate-300 mb-3">
            分析类型
          </label>
          <div className="grid grid-cols-2 lg:grid-cols-5 gap-3">
            {Object.entries(analysisTypes).map(([type, selected]) => {
              const Icon = getAnalysisIcon(type);
              return (
                <label key={type} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selected}
                    onChange={(e) => handleAnalysisTypeChange(type, e.target.checked)}
                    className="rounded border-slate-600 bg-slate-700 text-blue-500"
                  />
                  <Icon className="w-4 h-4 text-slate-400" />
                  <span className="text-sm text-slate-300">{getAnalysisLabel(type)}</span>
                </label>
              );
            })}
          </div>
        </div>

        {/* 高级选项 */}
        <div className="mt-4">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={includeCompetitors}
              onChange={(e) => setIncludeCompetitors(e.target.checked)}
              className="rounded border-slate-600 bg-slate-700 text-blue-500"
            />
            <span className="text-sm text-slate-300">包含竞争对手分析</span>
          </label>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-4 mt-6">
          <button
            onClick={performAnalysis}
            disabled={isAnalyzing || !selectedEnterprise}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed px-4 py-2 rounded-lg text-white transition-colors"
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>分析中...</span>
              </>
            ) : (
              <>
                <Brain className="w-4 h-4" />
                <span>开始AI分析</span>
              </>
            )}
          </button>

          <button
            onClick={generateVisualizations}
            disabled={isGeneratingImages || !analysisResults}
            className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 disabled:bg-slate-600 disabled:cursor-not-allowed px-4 py-2 rounded-lg text-white transition-colors"
          >
            {isGeneratingImages ? (
              <>
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>生成中...</span>
              </>
            ) : (
              <>
                <Image className="w-4 h-4" />
                <span>生成可视化</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* 分析结果 */}
      {analysisResults && (
        <div className="bg-slate-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white">分析结果</h2>
            <div className="flex items-center space-x-2 text-sm text-green-400">
              <CheckCircle className="w-4 h-4" />
              <span>分析完成</span>
            </div>
          </div>

          {/* 分析摘要 */}
          <div className="bg-slate-700 rounded-lg p-4 mb-4">
            <h3 className="font-medium text-white mb-2">分析摘要</h3>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-slate-400">分析类型:</span>
                <span className="text-white ml-2">{(analysisResults as any).summary?.analysis?.types?.join(', ')}</span>
              </div>
              <div>
                <span className="text-slate-400">平均置信度:</span>
                <span className="text-white ml-2">{((analysisResults as any).summary?.analysis?.averageConfidence * 100)?.toFixed(1)}%</span>
              </div>
              <div>
                <span className="text-slate-400">处理时间:</span>
                <span className="text-white ml-2">{(analysisResults as any).totalProcessingTime}ms</span>
              </div>
              <div>
                <span className="text-slate-400">完成时间:</span>
                <span className="text-white ml-2">{new Date((analysisResults as any).summary?.analysis?.completedAt).toLocaleTimeString()}</span>
              </div>
            </div>
          </div>

          {/* 关键洞察 */}
          {(analysisResults as any).summary?.keyInsights && (
            <div className="bg-slate-700 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-white mb-2">关键洞察</h3>
              <ul className="space-y-1">
                {(analysisResults as any).summary.keyInsights.map((insight: string, index: number) => (
                  <li key={index} className="text-sm text-slate-300 flex items-start space-x-2">
                    <span className="text-blue-400 mt-1">•</span>
                    <span>{insight}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* 综合建议 */}
          {(analysisResults as any).summary?.overallRecommendations && (
            <div className="bg-slate-700 rounded-lg p-4">
              <h3 className="font-medium text-white mb-2">综合建议</h3>
              <ul className="space-y-1">
                {(analysisResults as any).summary.overallRecommendations.slice(0, 5).map((recommendation: string, index: number) => (
                  <li key={index} className="text-sm text-slate-300 flex items-start space-x-2">
                    <span className="text-green-400 mt-1">→</span>
                    <span>{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* 生成的图像 */}
      {generatedImages && (
        <div className="bg-slate-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-white mb-4">AI生成可视化</h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {Object.entries(generatedImages as any).map(([type, result]: [string, any]) => (
              <div key={type} className="bg-slate-700 rounded-lg p-4">
                <h3 className="font-medium text-white mb-2 capitalize">{type}</h3>
                {result.success && result.images ? (
                  <div>
                    <img 
                      src={result.images[0].url} 
                      alt={`${type} visualization`}
                      className="w-full h-48 object-cover rounded-lg mb-2"
                    />
                    <button className="flex items-center space-x-1 text-sm text-blue-400 hover:text-blue-300">
                      <Download className="w-3 h-3" />
                      <span>下载</span>
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-sm text-red-400">
                    <AlertCircle className="w-4 h-4" />
                    <span>生成失败</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 历史记录 */}
      {analysisHistory.length > 0 && (
        <div className="bg-slate-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-white mb-4">分析历史</h2>
          <div className="space-y-2">
            {analysisHistory.slice(0, 5).map((record: any, index) => (
              <div key={index} className="flex items-center justify-between bg-slate-700 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  <Clock className="w-4 h-4 text-slate-400" />
                  <span className="text-sm text-white">{record.analysis_type}</span>
                  <span className="text-xs text-slate-400">
                    {new Date(record.created_at).toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-slate-400">
                    置信度: {(record.confidence_score * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AIAnalysis;
