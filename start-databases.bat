@echo off
echo 🗄️ 启动企业画像师平台数据库服务
echo ================================

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Docker，请先安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('docker --version') do echo ✅ %%i
)

REM 检查Docker Compose是否可用
docker compose version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose不可用
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('docker compose version') do echo ✅ %%i
)

echo.
echo 🚀 启动数据库容器...
echo --------------------------------
echo MongoDB: http://localhost:27017
echo Neo4j Browser: http://localhost:7474 (用户名: neo4j, 密码: password)
echo PostgreSQL: localhost:5432 (用户名: postgres, 密码: password)
echo Redis: localhost:6379 (密码: password)
echo.

REM 启动数据库服务
docker compose -f docker-compose.dev.yml up -d

if %errorlevel% neq 0 (
    echo ❌ 数据库启动失败
    pause
    exit /b 1
)

echo.
echo ⏳ 等待数据库服务启动...
timeout /t 10 /nobreak >nul

echo.
echo 📊 检查服务状态...
docker compose -f docker-compose.dev.yml ps

echo.
echo ✅ 数据库服务已启动！
echo.
echo 📋 下一步操作:
echo 1. 初始化数据库: cd server && npm run db:setup
echo 2. 启动应用: npm run dev
echo 3. 停止数据库: docker compose -f docker-compose.dev.yml down
echo.
echo 🌐 访问地址:
echo - Neo4j Browser: http://localhost:7474
echo - 应用前端: http://localhost:5173 (启动后)
echo - 应用后端: http://localhost:3001 (启动后)
echo.
pause
