version: '3.8'

services:
  # MongoDB
  mongodb:
    image: mongo:7.0
    container_name: enterprise-profiler-mongodb-dev
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: enterprise_profiler
    volumes:
      - mongodb_dev_data:/data/db
    networks:
      - enterprise-dev-network

  # Neo4j
  neo4j:
    image: neo4j:5.15
    container_name: enterprise-profiler-neo4j-dev
    restart: unless-stopped
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      NEO4J_AUTH: neo4j/password
      NEO4J_PLUGINS: '["apoc"]'
      NEO4J_dbms_security_procedures_unrestricted: apoc.*
      NEO4J_dbms_memory_heap_initial__size: 512m
      NEO4J_dbms_memory_heap_max__size: 1G
    volumes:
      - neo4j_dev_data:/data
      - neo4j_dev_logs:/logs
    networks:
      - enterprise-dev-network

  # PostgreSQL
  postgresql:
    image: postgres:16
    container_name: enterprise-profiler-postgresql-dev
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: enterprise_analytics
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgresql_dev_data:/var/lib/postgresql/data
    networks:
      - enterprise-dev-network

  # Redis (for caching)
  redis:
    image: redis:7.2-alpine
    container_name: enterprise-profiler-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass password
    volumes:
      - redis_dev_data:/data
    networks:
      - enterprise-dev-network

volumes:
  mongodb_dev_data:
  neo4j_dev_data:
  neo4j_dev_logs:
  postgresql_dev_data:
  redis_dev_data:

networks:
  enterprise-dev-network:
    driver: bridge
